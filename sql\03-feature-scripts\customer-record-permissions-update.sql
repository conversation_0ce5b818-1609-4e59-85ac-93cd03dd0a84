-- =====================================================================
--   客户跟进记录权限完善SQL脚本
--   目的：修复权限配置问题并完善跟进记录功能的细粒度权限控制
--   作者：SmartAdmin权限优化
--   日期：2025-06-16
-- =====================================================================

-- 1. 修复"获取统计信息"权限点的api_perms字段
UPDATE t_menu 
SET api_perms = 'customer:view:statistics' 
WHERE menu_id = 3093 AND api_perms = '/customer/view/statistics';

-- 2. 为了更好的权限控制，将"管理跟进记录"拆分为更细粒度的权限点
-- 首先检查当前最大的menu_id，确保不冲突
-- SELECT MAX(menu_id) FROM t_menu; -- 当前应该是3093左右

-- 2.1 删除原有的粗粒度"管理跟进记录"权限（如果需要的话，可以保留作为父级权限）
-- 这里我们保留它作为查看跟进记录的权限，并修改其含义
UPDATE t_menu 
SET 
    menu_name = '查看跟进记录',
    api_perms = 'customer:record:view',
    web_perms = 'customer:record:view'
WHERE menu_id = 3092;

-- 2.2 添加新的细粒度权限点
INSERT INTO t_menu (
    menu_id, menu_name, menu_type, parent_id, sort, path, component,
    perms_type, api_perms, web_perms, icon, context_menu_id,
    frame_flag, frame_url, cache_flag, visible_flag, disabled_flag,
    deleted_flag, create_user_id, create_time, update_user_id, update_time
) VALUES 
-- 添加跟进记录权限
(3094, '添加跟进记录', 3, 3088, 5, null, null,
 2, 'customer:record:add', 'customer:record:add', null, null,
 0, null, 0, 1, 0,
 0, 1, NOW(), null, NOW()),

-- 修改跟进记录权限  
(3095, '修改跟进记录', 3, 3088, 6, null, null,
 2, 'customer:record:edit', 'customer:record:edit', null, null,
 0, null, 0, 1, 0,
 0, 1, NOW(), null, NOW()),

-- 删除跟进记录权限
(3096, '删除跟进记录', 3, 3088, 7, null, null,
 2, 'customer:record:delete', 'customer:record:delete', null, null,
 0, null, 0, 1, 0,
 0, 1, NOW(), null, NOW()),

-- 查看订单明细权限（客户跟进记录中的订单明细标签页）
(3097, '查看订单明细', 3, 3088, 8, null, null,
 2, 'customer:order:view', 'customer:order:view', null, null,
 0, null, 0, 1, 0,
 0, 1, NOW(), null, NOW());

-- 3. 更新获取统计信息权限的排序，让新权限有序排列
UPDATE t_menu 
SET sort = 9
WHERE menu_id = 3093;

-- 4. 验证权限配置
SELECT 
    menu_id, 
    menu_name, 
    menu_type, 
    parent_id, 
    sort, 
    api_perms, 
    web_perms 
FROM t_menu 
WHERE parent_id = 3088 
ORDER BY sort;

-- 输出完成信息
SELECT '客户跟进记录权限配置已完成！' AS '操作结果',
       '请检查上述查询结果确认权限配置正确' AS '注意事项'; 