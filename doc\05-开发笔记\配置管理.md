# SmartAdmin 配置管理指南

## 📋 概述

本文档记录了 SmartAdmin 项目配置管理的重要经验和注意事项，帮助开发者正确配置和维护项目。

## 🗄️ 数据库连接配置

### 重要经验：双重配置文件问题

在修改数据库连接信息时，**必须同时修改两个位置的配置文件**：

#### 配置文件位置
1. **主配置文件位置**
   ```
   smart-admin-api-java17-springboot3/sa-base/src/main/resources/dev/sa-base.yaml
   ```

2. **根目录配置文件位置**
   ```
   sa-base/src/main/resources/dev/sa-base.yaml
   ```

#### 为什么需要修改两个地方？

1. **项目结构原因**：
   - SmartAdmin 项目采用了模块化架构
   - `sa-base` 模块作为基础模块，被其他模块依赖
   - 项目根目录和子模块目录都有独立的配置文件

2. **配置加载机制**：
   - Spring Boot 会按照特定顺序加载配置文件
   - 不同位置的配置文件可能在不同的构建阶段被使用
   - 为确保配置一致性，两个位置都需要保持同步

3. **构建和部署**：
   - Maven 构建时可能会从不同位置复制配置文件
   - 开发环境和生产环境可能使用不同的配置文件路径

### 标准数据库配置模板

```yaml
spring:
  datasource:
    url: ****************************************************************************************************************************************************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    initial-size: 2
    min-idle: 2
    max-active: 10
    max-wait: 60000
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 300000
    filters: stat
```

### 关键参数说明

- **allowPublicKeyRetrieval=true**：解决 MySQL 8.0 的 `caching_sha2_password` 认证问题
- **useSSL=false**：在开发环境中禁用 SSL 连接
- **serverTimezone=Asia/Shanghai**：设置时区避免时间问题
- **characterEncoding=UTF-8**：确保中文字符正确处理
- **p6spy**：SQL 监控工具，用于开发环境调试

## 🔧 多数据源配置

### 外部数据源配置

SmartAdmin 支持多数据源配置，用于连接外部业务数据库：

```yaml
# 外部数据源配置
lirun:
  datasource:
    url: *************************************
    username: lirun_user
    password: lirun_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 配置加载方式

```java
@Value("${lirun.datasource.url}")
private String lirunUrl;

@Value("${lirun.datasource.username}")
private String lirunUsername;

@Value("${lirun.datasource.password}")
private String lirunPassword;
```

## 🌍 环境配置管理

### 环境配置文件结构

```
sa-base/src/main/resources/
├── dev/
│   └── sa-base.yaml          # 开发环境配置
├── test/
│   └── sa-base.yaml          # 测试环境配置
├── prod/
│   └── sa-base.yaml          # 生产环境配置
└── application.yaml          # 基础配置
```

### 环境切换

通过 Maven Profile 切换环境：

```bash
# 开发环境
mvn clean package -Pdev

# 测试环境
mvn clean package -Ptest

# 生产环境
mvn clean package -Pprod
```

## 📁 文件存储配置

### 本地文件存储

```yaml
file:
  storage:
    local:
      upload-path: /opt/smartadmin/uploads/
      url-prefix: /uploads/
```

### Docker 环境配置

```yaml
# docker-compose.yml 环境变量覆盖
environment:
  - FILE_STORAGE_LOCAL_UPLOAD_PATH=/usr/share/nginx/html/uploads/
  - FILE_STORAGE_LOCAL_URL_PREFIX=/uploads/
```

## 🔐 安全配置

### Sa-Token 配置

```yaml
sa-token:
  token-name: satoken
  timeout: 2592000
  activity-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false
```

### 跨域配置

```yaml
cors:
  allowed-origins: "*"
  allowed-methods: "*"
  allowed-headers: "*"
  allow-credentials: true
```

## 📊 监控配置

### Actuator 配置

```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
```

### 日志配置

```yaml
logging:
  level:
    net.lab1024.sa: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

## ✅ 配置修改检查清单

每次修改配置时，请按以下步骤操作：

### 数据库配置修改
1. ✅ 修改主配置文件
2. ✅ 修改根目录配置文件
3. ✅ 确保两个文件的配置完全一致
4. ✅ 重启后端服务
5. ✅ 测试数据库连接是否正常

### 环境配置修改
1. ✅ 确认目标环境
2. ✅ 修改对应环境的配置文件
3. ✅ 验证配置语法正确性
4. ✅ 重新构建和部署
5. ✅ 验证功能正常

## 🚨 常见问题和解决方案

### 1. 数据库连接问题

#### 权限问题
```
Access denied for user 'root'@'IP地址'
```
**解决方案**：检查数据库用户权限，确保用户可以从指定IP连接

#### 认证问题
```
Public Key Retrieval is not allowed
```
**解决方案**：在连接URL中添加 `allowPublicKeyRetrieval=true`

#### 时区问题
```
The server time zone value is unrecognized
```
**解决方案**：在连接URL中添加 `serverTimezone=Asia/Shanghai`

### 2. 配置不生效问题

**可能原因**：
- 只修改了一个位置的配置文件
- 环境变量覆盖了配置文件设置
- 缓存未清理

**解决方案**：
- 检查并同步所有配置文件
- 确认环境变量设置
- 清理缓存并重启服务

### 3. 文件上传问题

**可能原因**：
- 文件存储路径配置错误
- 目录权限不足
- Nginx 配置不匹配

**解决方案**：
- 检查文件存储路径配置
- 确保目录有读写权限
- 同步 Nginx 静态文件配置

## 💡 最佳实践

### 配置管理原则
1. **环境隔离**：不同环境使用独立的配置文件
2. **敏感信息**：敏感配置使用环境变量或加密存储
3. **版本控制**：配置文件纳入版本控制，但排除敏感信息
4. **文档同步**：配置变更及时更新文档

### 配置验证
1. **语法检查**：使用 YAML 语法检查工具
2. **连接测试**：配置修改后立即测试连接
3. **功能验证**：确保相关功能正常工作
4. **回滚准备**：保留配置修改前的备份

---

💡 **提示**：配置管理是项目稳定运行的基础，请严格按照规范进行配置修改，并做好备份和测试工作。
