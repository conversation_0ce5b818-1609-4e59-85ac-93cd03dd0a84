package net.lab1024.sa.admin.module.business.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户跟进记录VO
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:00:00
 * @Copyright 1.0
 */
@Data
public class CustomerRecordVO {

    @Schema(description = "跟进记录ID")
    private Long recordId;

    @Schema(description = "客户唯一编码")
    private String customerUniqueCode;

    @Schema(description = "跟进类型")
    private String type;

    @Schema(description = "跟进状态")
    private String status;

    @Schema(description = "跟进时间")
    private LocalDateTime followTime;

    @Schema(description = "下次跟进时间")
    private LocalDateTime nextFollowTime;

    @Schema(description = "跟进内容")
    private String content;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 