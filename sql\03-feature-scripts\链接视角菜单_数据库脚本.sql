-- 链接视角菜单数据库脚本
-- 请在数据库中执行以下SQL语句来添加链接视角菜单

-- 1. 添加链接视角主菜单
INSERT INTO t_menu (
    menu_name, menu_type, parent_id, sort, path, component, 
    perms_type, api_perms, web_perms, icon, context_menu_id, 
    frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, 
    deleted_flag, create_user_id, create_time, update_user_id, update_time
) VALUES (
    '链接视角', 2, 306, 2, '/business/customer/link', '/business/customer/customer-link.vue',
    1, null, null, 'LinkOutlined', null,
    0, null, 1, 1, 0,
    0, 1, NOW(), null, NOW()
);

-- 2. 获取刚插入的链接视角菜单ID（假设为308，请根据实际情况调整）
-- SELECT LAST_INSERT_ID();

-- 3. 添加链接视角的功能权限点
-- 3.1 查询链接数据权限
INSERT INTO t_menu (
    menu_name, menu_type, parent_id, sort, path, component,
    perms_type, api_perms, web_perms, icon, context_menu_id,
    frame_flag, frame_url, cache_flag, visible_flag, disabled_flag,
    deleted_flag, create_user_id, create_time, update_user_id, update_time
) VALUES (
    '查询链接', 3, (SELECT menu_id FROM (SELECT menu_id FROM t_menu WHERE menu_name = '链接视角' AND parent_id = 306) AS temp), 1, null, null,
    2, '/customer/link/queryPage', 'customer:link:query', null, null,
    0, null, 0, 1, 0,
    0, 1, NOW(), null, NOW()
);

-- 3.2 查看链接详情权限
INSERT INTO t_menu (
    menu_name, menu_type, parent_id, sort, path, component,
    perms_type, api_perms, web_perms, icon, context_menu_id,
    frame_flag, frame_url, cache_flag, visible_flag, disabled_flag,
    deleted_flag, create_user_id, create_time, update_user_id, update_time
) VALUES (
    '查看链接详情', 3, (SELECT menu_id FROM (SELECT menu_id FROM t_menu WHERE menu_name = '链接视角' AND parent_id = 306) AS temp), 2, null, null,
    2, '/customer/link/detail', 'customer:link:detail', null, null,
    0, null, 0, 1, 0,
    0, 1, NOW(), null, NOW()
);

-- 3.3 管理链接关系权限
INSERT INTO t_menu (
    menu_name, menu_type, parent_id, sort, path, component,
    perms_type, api_perms, web_perms, icon, context_menu_id,
    frame_flag, frame_url, cache_flag, visible_flag, disabled_flag,
    deleted_flag, create_user_id, create_time, update_user_id, update_time
) VALUES (
    '管理链接关系', 3, (SELECT menu_id FROM (SELECT menu_id FROM t_menu WHERE menu_name = '链接视角' AND parent_id = 306) AS temp), 3, null, null,
    2, '/customer/link/manage', 'customer:link:manage', null, null,
    0, null, 0, 1, 0,
    0, 1, NOW(), null, NOW()
);

-- 4. 为管理员角色分配新菜单权限（假设管理员角色ID为1）
-- 首先获取链接视角菜单ID
SET @link_menu_id = (SELECT menu_id FROM t_menu WHERE menu_name = '链接视角' AND parent_id = 306);

-- 为管理员角色分配链接视角主菜单权限
INSERT INTO t_role_menu (role_id, menu_id, create_user_id, create_time, update_user_id, update_time)
VALUES (1, @link_menu_id, 1, NOW(), null, NOW());

-- 为管理员角色分配链接视角所有子权限
INSERT INTO t_role_menu (role_id, menu_id, create_user_id, create_time, update_user_id, update_time)
SELECT 1, menu_id, 1, NOW(), null, NOW()
FROM t_menu 
WHERE parent_id = @link_menu_id;

-- 5. 验证菜单是否添加成功
-- SELECT * FROM t_menu WHERE parent_id = 306 ORDER BY sort;
-- SELECT * FROM t_menu WHERE parent_id = @link_menu_id ORDER BY sort; 