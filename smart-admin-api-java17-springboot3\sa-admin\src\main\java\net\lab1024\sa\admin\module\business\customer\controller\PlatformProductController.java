package net.lab1024.sa.admin.module.business.customer.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.customer.domain.form.PlatformProductQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.PlatformProductVO;
import net.lab1024.sa.admin.module.business.customer.service.PlatformProductService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 平台货品Controller
 *
 * @Author: 汪波
 * @Date: 2025-01-16 09:00:00
 * @Copyright: 1.0
 */
@Tag(name = "平台货品管理")
@RestController
@RequestMapping("/platform/product")
public class PlatformProductController {
    
    @Autowired
    private PlatformProductService platformProductService;
    
    @Operation(summary = "查询平台货品列表")
    @PostMapping("/queryPage")
    public ResponseDTO<PageResult<PlatformProductVO>> queryPage(@RequestBody @Valid PlatformProductQueryForm queryForm) {
        return ResponseDTO.ok(platformProductService.queryPage(queryForm));
    }
    
    @Operation(summary = "获取所有店铺名称")
    @PostMapping("/getAllShopNames")
    public ResponseDTO<List<String>> getAllShopNames() {
        return ResponseDTO.ok(platformProductService.getAllShopNames());
    }
} 