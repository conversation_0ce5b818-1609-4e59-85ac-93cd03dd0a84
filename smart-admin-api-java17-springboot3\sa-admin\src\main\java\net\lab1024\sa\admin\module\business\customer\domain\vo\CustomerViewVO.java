package net.lab1024.sa.admin.module.business.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 客户视角 VO
 *
 * <AUTHOR>
 * @Date 2025-01-09 15:00:00
 * @Copyright 1.0
 */

@Data
public class CustomerViewVO {

    @Schema(description = "客户唯一编码")
    private String 客户唯一编码;

    @Schema(description = "总销售额")
    private BigDecimal 总销售额;

    @Schema(description = "总成本")
    private BigDecimal 总成本;

    @Schema(description = "总利润")
    private BigDecimal 总利润;

    @Schema(description = "总客户毛利率")
    private String 总客户毛利率;

    @Schema(description = "购买次数")
    private Integer 购买次数;

    @Schema(description = "复购周期（天）")
    private Integer 复购周期;

    @Schema(description = "首次成交日期")
    private LocalDate 首次成交日期;

    @Schema(description = "上次购买日期")
    private LocalDate 上次购买日期;

    @Schema(description = "流失风险")
    private String 流失风险;

    @Schema(description = "上次购买后距今天数")
    private Integer 上次购买后距今天数;

    @Schema(description = "购买天数")
    private Integer 购买天数;

    @Schema(description = "最近一次下单店铺")
    private String 最近一次下单店铺;

    @Schema(description = "最近下单原始单号")
    private String 最近下单原始单号;

    @Schema(description = "会员等级")
    private String 会员等级;
}
