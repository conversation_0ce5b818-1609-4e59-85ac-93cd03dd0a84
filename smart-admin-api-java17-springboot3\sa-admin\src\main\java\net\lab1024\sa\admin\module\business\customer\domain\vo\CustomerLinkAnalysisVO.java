package net.lab1024.sa.admin.module.business.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 链接视角 - 整体分析结果VO
 *
 * <AUTHOR>
 * @Date 2025-01-18 10:15:00
 * @Copyright 1.0
 */
@Data
@Schema(description = "链接分析统计 VO")
public class CustomerLinkAnalysisVO {

    @Schema(description = "订单数量")
    private Long orderCount;

    @Schema(description = "付款人数")
    private Long paymentUserCount;

    @Schema(description = "目标客户人数（基于付款日期范围，不受回购日期影响）")
    private Long targetCustomerCount;

    @Schema(description = "复购人数")
    private Long repurchaseUserCount;

    @Schema(description = "目标复购人数（基于付款日期范围，不受回购日期影响）")
    private Long targetRepurchaseUserCount;

    @Schema(description = "复购率（基于付款日期范围）")
    private BigDecimal repurchaseRate;

    @Schema(description = "验证后复购人数（在回购日期期间内有复购行为的客户数）")
    private Long validatedRepurchaseUserCount;

    @Schema(description = "验证后复购率（基于回购日期验证）")
    private BigDecimal validatedRepurchaseRate;

    @Schema(description = "平均复购周期")
    private BigDecimal avgRepurchaseCycle;

    @Schema(description = "最小复购间隔")
    private Integer minRepurchaseInterval;

    @Schema(description = "最大复购间隔")
    private Integer maxRepurchaseInterval;

    @Schema(description = "平均复购间隔")
    private BigDecimal avgRepurchaseInterval;
} 