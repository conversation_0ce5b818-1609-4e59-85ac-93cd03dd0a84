-- =====================================================================
--   客户分类预计算增强方案
--   目的：为 crm_客户查询 表添加客户分类字段，提升链接视角查询性能
--   作者：SmartAdmin性能优化
--   日期：2025-07-30
-- =====================================================================

-- 1. 为 crm_客户查询 表添加客户分类相关字段
ALTER TABLE smart_admin_v3.crm_客户查询 
ADD COLUMN 客户类型 ENUM('新客', '老客') DEFAULT NULL COMMENT '客户类型：新客-首次购买，老客-重复购买',
ADD COLUMN 分类更新时间 DATETIME DEFAULT NULL COMMENT '客户分类最后更新时间',
ADD COLUMN 分类基准日期 DATE DEFAULT NULL COMMENT '用于计算客户分类的基准日期';

-- 2. 添加索引优化查询性能
ALTER TABLE smart_admin_v3.crm_客户查询 
ADD INDEX idx_customer_type (客户类型),
ADD INDEX idx_classification_update (分类更新时间),
ADD INDEX idx_purchase_count_type (购买次数, 客户类型);

-- 3. 创建客户分类更新日志表（用于追踪更新历史）
CREATE TABLE IF NOT EXISTS smart_admin_v3.t_customer_classification_log (
    log_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    update_date DATE NOT NULL COMMENT '更新日期',
    total_customers INT DEFAULT 0 COMMENT '总客户数',
    new_customers INT DEFAULT 0 COMMENT '新客数量',
    old_customers INT DEFAULT 0 COMMENT '老客数量',
    updated_customers INT DEFAULT 0 COMMENT '本次更新的客户数',
    execution_time_ms BIGINT DEFAULT 0 COMMENT '执行时间（毫秒）',
    status ENUM('SUCCESS', 'FAILED', 'PARTIAL') DEFAULT 'SUCCESS' COMMENT '执行状态',
    error_message TEXT DEFAULT NULL COMMENT '错误信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户分类更新日志表';

-- 4. 创建客户分类计算存储过程
DELIMITER $$

CREATE PROCEDURE smart_admin_v3.UpdateCustomerClassification(
    IN p_base_date DATE,
    IN p_batch_size INT DEFAULT 1000
)
BEGIN
    DECLARE v_total_customers INT DEFAULT 0;
    DECLARE v_new_customers INT DEFAULT 0;
    DECLARE v_old_customers INT DEFAULT 0;
    DECLARE v_updated_customers INT DEFAULT 0;
    DECLARE v_start_time BIGINT DEFAULT 0;
    DECLARE v_execution_time BIGINT DEFAULT 0;
    DECLARE v_error_message TEXT DEFAULT NULL;
    DECLARE v_status VARCHAR(20) DEFAULT 'SUCCESS';
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_customer_code VARCHAR(255);
    DECLARE v_historical_purchases INT;
    DECLARE v_new_type ENUM('新客', '老客');
    
    -- 游标定义
    DECLARE customer_cursor CURSOR FOR 
        SELECT 客户唯一编码 
        FROM smart_admin_v3.crm_客户查询 
        WHERE 分类更新时间 IS NULL 
           OR 分类基准日期 != p_base_date 
           OR 分类更新时间 < DATE_SUB(NOW(), INTERVAL 1 DAY)
        LIMIT p_batch_size;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION 
    BEGIN
        GET DIAGNOSTICS CONDITION 1
            v_error_message = MESSAGE_TEXT;
        SET v_status = 'FAILED';
        ROLLBACK;
    END;
    
    SET v_start_time = UNIX_TIMESTAMP(NOW(3)) * 1000;
    
    START TRANSACTION;
    
    -- 打开游标并处理客户分类
    OPEN customer_cursor;
    
    customer_loop: LOOP
        FETCH customer_cursor INTO v_customer_code;
        
        IF done THEN
            LEAVE customer_loop;
        END IF;
        
        -- 计算该客户在基准日期之前的历史购买次数
        SELECT COUNT(DISTINCT DATE(付款时间))
        INTO v_historical_purchases
        FROM lirun.订单明细
        WHERE 客户唯一编码 = v_customer_code
          AND 订单状态 != '线下退款'
          AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
          AND 标记名称 != '贴贴贴贴贴'
          AND DATE(付款时间) < p_base_date;
        
        -- 根据历史购买次数确定客户类型
        IF v_historical_purchases = 0 THEN
            SET v_new_type = '新客';
            SET v_new_customers = v_new_customers + 1;
        ELSE
            SET v_new_type = '老客';
            SET v_old_customers = v_old_customers + 1;
        END IF;
        
        -- 更新客户分类信息
        UPDATE smart_admin_v3.crm_客户查询 
        SET 客户类型 = v_new_type,
            分类更新时间 = NOW(),
            分类基准日期 = p_base_date
        WHERE 客户唯一编码 = v_customer_code;
        
        SET v_updated_customers = v_updated_customers + 1;
        
    END LOOP;
    
    CLOSE customer_cursor;
    
    -- 获取总客户数
    SELECT COUNT(*) INTO v_total_customers FROM smart_admin_v3.crm_客户查询;
    
    SET v_execution_time = UNIX_TIMESTAMP(NOW(3)) * 1000 - v_start_time;
    
    -- 记录更新日志
    INSERT INTO smart_admin_v3.t_customer_classification_log (
        update_date, total_customers, new_customers, old_customers, 
        updated_customers, execution_time_ms, status, error_message
    ) VALUES (
        p_base_date, v_total_customers, v_new_customers, v_old_customers,
        v_updated_customers, v_execution_time, v_status, v_error_message
    );
    
    IF v_status = 'SUCCESS' THEN
        COMMIT;
        SELECT CONCAT('客户分类更新完成！更新客户数：', v_updated_customers, 
                     '，新客：', v_new_customers, '，老客：', v_old_customers,
                     '，执行时间：', v_execution_time, 'ms') AS result;
    ELSE
        ROLLBACK;
        SELECT CONCAT('客户分类更新失败：', IFNULL(v_error_message, '未知错误')) AS result;
    END IF;
    
END$$

DELIMITER ;

-- 5. 创建增量更新存储过程（用于日常维护）
DELIMITER $$

CREATE PROCEDURE smart_admin_v3.IncrementalUpdateCustomerClassification(
    IN p_days_back INT DEFAULT 7
)
BEGIN
    DECLARE v_base_date DATE;
    DECLARE v_updated_count INT DEFAULT 0;
    
    -- 设置基准日期为指定天数前
    SET v_base_date = DATE_SUB(CURDATE(), INTERVAL p_days_back DAY);
    
    -- 只更新最近有订单活动的客户
    UPDATE smart_admin_v3.crm_客户查询 c
    SET 
        客户类型 = CASE 
            WHEN (
                SELECT COUNT(DISTINCT DATE(付款时间))
                FROM lirun.订单明细 o
                WHERE o.客户唯一编码 = c.客户唯一编码
                  AND o.订单状态 != '线下退款'
                  AND NOT (o.订单来源 = '手工创建' AND o.订单状态 = '已取消')
                  AND o.标记名称 != '贴贴贴贴贴'
                  AND DATE(o.付款时间) < v_base_date
            ) = 0 THEN '新客'
            ELSE '老客'
        END,
        分类更新时间 = NOW(),
        分类基准日期 = v_base_date
    WHERE c.客户唯一编码 IN (
        SELECT DISTINCT 客户唯一编码 
        FROM lirun.订单明细 
        WHERE DATE(付款时间) >= DATE_SUB(CURDATE(), INTERVAL p_days_back DAY)
          AND 订单状态 != '线下退款'
          AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
          AND 标记名称 != '贴贴贴贴贴'
    );
    
    SET v_updated_count = ROW_COUNT();
    
    SELECT CONCAT('增量更新完成！更新客户数：', v_updated_count) AS result;
    
END$$

DELIMITER ;

-- 6. 初始化所有客户的分类数据（首次运行）
-- 注意：这个操作可能需要较长时间，建议在业务低峰期执行
-- CALL smart_admin_v3.UpdateCustomerClassification(CURDATE(), 1000);

-- 7. 验证表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'smart_admin_v3' 
  AND TABLE_NAME = 'crm_客户查询'
  AND COLUMN_NAME IN ('客户类型', '分类更新时间', '分类基准日期')
ORDER BY ORDINAL_POSITION;

-- 输出完成信息
SELECT '客户分类预计算增强方案部署完成！' AS '操作结果',
       '请执行存储过程进行初始化：CALL smart_admin_v3.UpdateCustomerClassification(CURDATE(), 1000);' AS '下一步操作';
