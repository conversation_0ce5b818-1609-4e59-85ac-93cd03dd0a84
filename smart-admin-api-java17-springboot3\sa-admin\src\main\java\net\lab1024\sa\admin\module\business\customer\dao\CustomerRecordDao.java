package net.lab1024.sa.admin.module.business.customer.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.customer.domain.entity.CustomerRecordEntity;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerRecordQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 客户跟进记录DAO
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:00:00
 * @Copyright 1.0
 */
@Mapper
public interface CustomerRecordDao extends BaseMapper<CustomerRecordEntity> {

    /**
     * 分页查询客户跟进记录
     */
    Page<CustomerRecordVO> queryPage(Page<?> page, @Param("queryForm") CustomerRecordQueryForm queryForm);
} 