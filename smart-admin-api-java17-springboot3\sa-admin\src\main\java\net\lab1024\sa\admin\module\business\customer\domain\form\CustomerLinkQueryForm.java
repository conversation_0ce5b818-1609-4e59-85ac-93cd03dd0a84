package net.lab1024.sa.admin.module.business.customer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 链接视角查询表单
 *
 * <AUTHOR>
 * @Date 2025-01-18 10:00:00
 * @Copyright 1.0
 */
@Data
@Schema(description = "客户链接查询表单")
public class CustomerLinkQueryForm {

    @Schema(description = "要排除的客服标旗")
    private String excludeFlag;

    @Schema(description = "要查询的平台货品ID列表", name = "selectedProductIds")
    private List<String> platformGoodsIds;

    @Schema(description = "付款开始日期", name = "paymentDateBegin")
    private String startDate;

    @Schema(description = "付款结束日期", name = "paymentDateEnd")
    private String endDate;

    @Schema(description = "客户特征类型: ''(不限), '新客', '老客'")
    private String customerType;

    @Schema(description = "回购日期开始", name = "repurchaseDateBegin")
    private String repurchaseDateBegin;

    @Schema(description = "回购日期结束", name = "repurchaseDateEnd")
    private String repurchaseDateEnd;
} 