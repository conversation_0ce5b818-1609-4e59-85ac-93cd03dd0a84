# 📊 SmartAdmin SQL 脚本管理

## 📋 概述

本目录包含了 SmartAdmin 项目的所有数据库相关脚本，按照功能和用途进行分类管理。

## 🗂️ 目录结构

### 📦 01-database-init (数据库初始化)
包含项目的核心数据库结构和初始数据脚本。

- **`smart_admin_v3.sql`** - 完整的数据库初始化脚本
  - 包含所有表结构定义
  - 包含基础数据和配置数据
  - 用于新环境的数据库初始化

### 🔄 02-version-updates (版本更新脚本)
按版本号组织的数据库升级脚本，用于现有环境的版本升级。

- **`v3.15.0.sql`** - v3.15.0 版本数据库更新脚本
  - 字典表结构重构
  - 新增字典数据管理功能
  
- **`v3.18.0.sql`** - v3.18.0 版本数据库更新脚本
  - 功能增强和优化

### 🔧 03-feature-scripts (功能脚本)
特定功能模块的数据库脚本，包括菜单权限、表结构等。

- **`create_customer_record_table.sql`** - 客户跟进记录表
  - 创建客户跟进记录功能相关表结构
  - 包含测试数据
  - 支持客户关系管理功能

- **`链接视角菜单_数据库脚本.sql`** - 链接视角菜单配置
  - 添加链接视角主菜单
  - 配置相关权限点
  - 为管理员角色分配权限

- **`链接视角权限完善_数据库脚本.sql`** - 链接视角权限完善
  - 完善链接视角页面权限控制
  - 添加查询、重置、统计、下载等权限点
  - 权限验证和配置脚本

- **`Menu.sql.vm`** - 菜单生成模板
  - 代码生成器使用的菜单SQL模板
  - 自动生成菜单和权限配置
  - 支持自定义菜单结构

- **`customer-record-permissions-update.sql`** - 客户跟进记录权限完善
  - 修复权限配置问题
  - 完善跟进记录功能的细粒度权限控制
  - 拆分粗粒度权限为更精细的操作权限

### 🗑️ 04-deprecated (已废弃脚本)
不再使用的历史脚本，保留用于参考。

- **`customer_classification_enhancement.sql`** - 客户分类预计算方案（已废弃）
  - 原客户分类预计算增强方案
  - 已被实时计算方案替代
  - 保留用于历史参考

## 📝 使用指南

### 新环境部署
1. 执行 `01-database-init/smart_admin_v3.sql` 初始化数据库
2. 根据需要执行 `03-feature-scripts/` 中的功能脚本

### 版本升级
1. 按版本顺序执行 `02-version-updates/` 中的升级脚本
2. 检查是否需要执行新的功能脚本

### 功能开发
1. 新功能的数据库脚本放入 `03-feature-scripts/`
2. 版本发布时的结构变更放入 `02-version-updates/`
3. 废弃的脚本移动到 `04-deprecated/`

## ⚠️ 注意事项

### 执行顺序
- 严格按照版本号顺序执行升级脚本
- 功能脚本可以独立执行，但需要检查依赖关系

### 备份策略
- 执行任何数据库脚本前，务必备份数据库
- 生产环境执行前，先在测试环境验证

### 权限管理
- 菜单和权限相关脚本执行后需要重启后端服务
- 用户需要重新登录以获取最新权限

### 字符集问题
- 注意 `utf8mb4_0900_ai_ci` 和 `utf8mb4_general_ci` 的兼容性
- 跨数据库查询时可能需要使用 CONVERT 和 COLLATE 函数

## 🔍 脚本验证

### 执行前检查
```sql
-- 检查当前数据库版本
SELECT * FROM t_config WHERE config_key = 'system_version';

-- 检查表是否存在
SHOW TABLES LIKE 't_table_name';

-- 检查字段是否存在
DESCRIBE t_table_name;
```

### 执行后验证
```sql
-- 验证表结构
SHOW CREATE TABLE t_table_name;

-- 验证数据完整性
SELECT COUNT(*) FROM t_table_name;

-- 验证权限配置
SELECT * FROM t_menu WHERE menu_name = '功能名称';
```

## 📚 相关文档

- [数据库设计规范](../doc/02-开发规范/数据库设计规范.md)
- [链接视角模块文档](../doc/04-功能模块/链接视角模块.md)
- [客户管理模块文档](../doc/04-功能模块/客户管理模块.md)

## 🏷️ 维护信息

- **最后更新**: 2025-07-30
- **维护人员**: 开发团队
- **版本**: v2.0

---

💡 **提示**: 如果您在执行SQL脚本时遇到问题，请参考 [问题解决手册](../doc/05-开发笔记/问题解决.md) 或联系开发团队。
