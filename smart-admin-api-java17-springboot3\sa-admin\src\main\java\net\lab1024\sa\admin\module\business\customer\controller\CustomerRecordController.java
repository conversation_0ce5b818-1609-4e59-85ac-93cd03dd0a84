package net.lab1024.sa.admin.module.business.customer.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerRecordAddForm;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerRecordUpdateForm;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerRecordQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerRecordVO;
import net.lab1024.sa.admin.module.business.customer.service.CustomerRecordService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 客户跟进记录Controller
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:00:00
 * @Copyright 1.0
 */
@Tag(name = "客户跟进记录")
@RestController
@RequestMapping("/api/customer/record")
@OperateLog
public class CustomerRecordController {

    @Resource
    private CustomerRecordService customerRecordService;

    @Operation(summary = "分页查询客户跟进记录 <AUTHOR>
    @PostMapping("/page/query")
    @SaCheckPermission("customer:record:view")
    public ResponseDTO<PageResult<CustomerRecordVO>> queryPage(@RequestBody @Valid CustomerRecordQueryForm queryForm) {
        return ResponseDTO.ok(customerRecordService.queryPage(queryForm));
    }

    @Operation(summary = "添加客户跟进记录 <AUTHOR>
    @PostMapping("/add")
    @SaCheckPermission("customer:record:add")
    public ResponseDTO<String> add(@RequestBody @Valid CustomerRecordAddForm addForm) {
        return customerRecordService.add(addForm);
    }

    @Operation(summary = "更新客户跟进记录 <AUTHOR>
    @PostMapping("/update")
    @SaCheckPermission("customer:record:edit")
    public ResponseDTO<String> update(@RequestBody @Valid CustomerRecordUpdateForm updateForm) {
        return customerRecordService.update(updateForm);
    }

    @Operation(summary = "删除客户跟进记录 <AUTHOR>
    @GetMapping("/delete/{recordId}")
    @SaCheckPermission("customer:record:delete")
    public ResponseDTO<String> delete(@PathVariable Long recordId) {
        return customerRecordService.delete(recordId);
    }
} 