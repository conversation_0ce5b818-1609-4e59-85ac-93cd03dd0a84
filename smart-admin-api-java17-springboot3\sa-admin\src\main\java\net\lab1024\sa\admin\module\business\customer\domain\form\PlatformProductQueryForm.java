package net.lab1024.sa.admin.module.business.customer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 平台货品查询表单
 *
 * @Author: 汪波
 * @Date: 2025-01-16 09:00:00
 * @Copyright: 1.0
 */
@Data
public class PlatformProductQueryForm extends PageParam {
    
    @Schema(description = "搜索关键词(货品ID或店铺名称)")
    private String searchWord;
    
    @Schema(description = "店铺名称")
    private String shopName;
} 