package net.lab1024.sa.admin.module.business.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 客户订单明细VO
 *
 * <AUTHOR>
 * @Date 2025-01-15 20:00:00
 * @Copyright 1.0
 */
@Data
public class CustomerOrderVO {

    @Schema(description = "原始单号")
    private String originalOrderNo;

    @Schema(description = "商家编码")
    private String merchantCode;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "已付金额")
    private BigDecimal paidAmount;

    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    @Schema(description = "付款时间")
    private String paymentTime;

    @Schema(description = "客户唯一编码")
    private String customerUniqueCode;

    @Schema(description = "订单状态")
    private String orderStatus;

    @Schema(description = "客服备注")
    private String customerServiceRemark;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "货品名称")
    private String goodsName;

    @Schema(description = "应收金额")
    private BigDecimal receivableAmount;

    @Schema(description = "交易时间")
    private String tradeTime;

    @Schema(description = "发货时间")
    private String deliveryTime;

    @Schema(description = "客户网名")
    private String customerNickname;

    @Schema(description = "收件人")
    private String consignee;

    @Schema(description = "省市县")
    private String province;

    @Schema(description = "手机号")
    private String phone;
} 