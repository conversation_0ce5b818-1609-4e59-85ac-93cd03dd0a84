package net.lab1024.sa.admin.module.business.customer.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 客户信息 实体类（基于crmk客户视角表设计）
 *
 * <AUTHOR>
 * @Date 2025-01-09 10:00:00
 * @Copyright 1.0
 */

@Data
@TableName("t_customer")
public class CustomerEntity {

    /**
     * 客户ID
     */
    @TableId(type = IdType.AUTO)
    private Long customerId;

    /**
     * 客户唯一编码
     */
    private String customerUniqueCode;

    /**
     * 总销售额
     */
    private BigDecimal totalSalesAmount;

    /**
     * 总货品成本
     */
    private BigDecimal totalCostAmount;

    /**
     * 总商品毛利
     */
    private BigDecimal totalProfitAmount;

    /**
     * 总商品毛利率
     */
    private String profitRate;

    /**
     * 累计购买次数
     */
    private Integer purchaseCount;

    /**
     * 复购周期（天）
     */
    private Integer repurchaseCycle;

    /**
     * 首次成交日期
     */
    private LocalDate firstPurchaseDate;

    /**
     * 上次购买日期
     */
    private LocalDate lastPurchaseDate;

    /**
     * 流失风险：正常/已流失
     */
    private String churnRisk;

    /**
     * 上次购买距离今天数
     */
    private Integer daysSinceLastPurchase;

    /**
     * 累计天数（从首次到最后购买）
     */
    private Integer totalDays;

    /**
     * 最近一次成交订单号
     */
    private String latestOrderNo;

    /**
     * 最近一次成交店铺
     */
    private String latestShopName;

    /**
     * 数据版本
     */
    private String dataVersion;

    /**
     * 删除状态：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
