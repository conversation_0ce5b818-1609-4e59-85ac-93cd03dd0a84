-- 创建客户跟进记录表
-- Author: 汪波
-- Date: 2025-01-14 15:00:00

DROP TABLE IF EXISTS `t_customer_record`;

CREATE TABLE `t_customer_record` (
    `record_id` bigint NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
    `customer_unique_code` varchar(255) NOT NULL COMMENT '客户唯一编码',
    `type` varchar(50) NOT NULL COMMENT '跟进类型：phone-电话沟通,email-邮件联系,visit-上门拜访,wechat-微信沟通,problem-问题处理,other-其他',
    `status` varchar(50) NOT NULL COMMENT '跟进状态：pending-待跟进,processing-跟进中,completed-已完成,cancelled-已取消',
    `follow_time` datetime NOT NULL COMMENT '跟进时间',
    `next_follow_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
    `content` text NOT NULL COMMENT '跟进内容',
    `remark` text DEFAULT NULL COMMENT '备注',
    `deleted_flag` tinyint(1) DEFAULT '0' COMMENT '删除状态：0-未删除，1-已删除',
    `create_user_id` bigint DEFAULT NULL COMMENT '创建人ID',
    `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`record_id`),
    KEY `idx_customer_code` (`customer_unique_code`),
    KEY `idx_follow_time` (`follow_time`),
    KEY `idx_type_status` (`type`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户跟进记录表';

-- 插入测试数据
INSERT INTO `t_customer_record` (`customer_unique_code`, `type`, `status`, `follow_time`, `next_follow_time`, `content`, `remark`, `deleted_flag`) VALUES
('CUST20240101001', 'phone', 'completed', '2025-01-14 10:30:00', '2025-01-21 10:00:00', '与客户电话沟通，了解最近的采购需求，客户表示下月可能会有大批量采购计划。', '客户态度积极', 0),
('CUST20240101001', 'email', 'processing', '2025-01-13 14:20:00', '2025-01-16 09:00:00', '发送产品报价单给客户，等待客户反馈。', '已发送详细报价', 0),
('CUST20240101001', 'visit', 'pending', '2025-01-12 16:00:00', '2025-01-18 14:00:00', '计划上门拜访客户，展示新产品样品。', '需要准备产品样品', 0); 