package net.lab1024.sa.admin.module.business.customer.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.customer.domain.entity.CustomerEntity;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerViewQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerViewVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 客户视角 DAO
 *
 * <AUTHOR>
 * @Date 2025-01-09 15:00:00
 * @Copyright 1.0
 */

@Mapper
public interface CustomerViewDao extends BaseMapper<CustomerEntity> {

    /**
     * 分页查询客户视角
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 客户列表
     */
    List<CustomerViewVO> queryPage(Page<?> page, @Param("queryForm") CustomerViewQueryForm queryForm);

    /**
     * 根据客户唯一编码查询详情
     *
     * @param customerUniqueCode 客户唯一编码
     * @return 客户详情
     */
    CustomerViewVO getDetail(@Param("customerId") String customerUniqueCode);

    /**
     * 获取客户统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getStatistics();

    /**
     * 导出查询
     *
     * @param queryForm 查询条件
     * @return 客户列表
     */
    List<CustomerViewVO> exportQuery(@Param("queryForm") CustomerViewQueryForm queryForm);
}
