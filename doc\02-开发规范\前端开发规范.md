# SmartAdmin 标准页面布局规范

## 概述

本文档定义了 SmartAdmin 系统中标准列表页面的布局规范，确保所有页面保持一致的视觉风格和用户体验。

## 标准布局结构

### 1. 整体布局层次

```
页面容器
├── 查询表单区域 (smart-query-form)
└── 卡片容器 (a-card)
    ├── 表格操作行 (TableOperator)
    ├── 数据表格 (a-table)
    └── 分页组件 (smart-query-table-page)
```

### 2. 查询表单区域

**位置**: 卡片外部，页面顶部
**样式类**: `smart-query-form`
**特点**:
- 背景色为白色
- 具有标准的内边距 (`padding: 5px 10px`)
- 底部间距 (`margin-bottom: 10px`)
- 使用 `smart-query-form-row` 和 `smart-query-form-item` 进行布局

**标准代码结构**:
```vue
<a-form class="smart-query-form" ref="queryFormRef">
    <a-row class="smart-query-form-row">
        <a-form-item label="字段名" class="smart-query-form-item">
            <a-input v-model:value="queryForm.field" placeholder="请输入" />
        </a-form-item>
        <a-form-item class="smart-query-form-item">
            <a-button type="primary" @click="onSearch">查询</a-button>
            <a-button @click="resetQuery" class="smart-margin-left10">重置</a-button>
        </a-form-item>
    </a-row>
</a-form>
```

### 3. 卡片容器

**样式属性**: `size="small" :bordered="false" :hoverable="true"`
**特点**:
- 小尺寸卡片，无边框，支持悬停效果
- 不添加额外的 margin-bottom（系统会自动处理间距）

### 4. 表格操作行

**位置**: 卡片内部顶部
**布局方式**: 右对齐 (`justify="end"`)
**组件**: 使用 `TableOperator` 组件
**样式**: 添加 `smart-margin-bottom5` 类

**标准代码结构**:
```vue
<a-row justify="end" ref="tableOperatorRef">
    <TableOperator 
        class="smart-margin-bottom5" 
        v-model="columns" 
        :tableId="TABLE_ID_CONST.XXX" 
        :refresh="queryData" 
    />
</a-row>
```

### 5. 数据表格

**配置**:
- `size="small"` - 小尺寸表格
- `:pagination="false"` - 禁用表格自带分页
- `:scroll="{ y: scrollY }"` - 启用垂直滚动
- `bordered` - 显示边框

### 6. 分页组件

**位置**: 卡片内部底部
**样式类**: `smart-query-table-page`
**特点**:
- 右对齐显示
- 顶部间距 (`margin-top: 10px`)
- 支持页面大小选择、快速跳转等功能

**标准代码结构**:
```vue
<div class="smart-query-table-page">
    <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
    />
</div>
```

## 表格高度自适应

### 配置要求

1. **引入工具函数**:
```javascript
import { calcTableHeight } from '/@/lib/table-auto-height';
```

2. **定义响应式变量**:
```javascript
const scrollY = ref(100);
const tableOperatorRef = ref();
const queryFormRef = ref();
```

3. **高度计算函数**:
```javascript
function autoCalcTableHeight() {
    calcTableHeight(scrollY, [tableOperatorRef, queryFormRef], 10);
}
```

4. **生命周期处理**:
```javascript
window.addEventListener('resize', autoCalcTableHeight);

onMounted(() => {
    queryData();
    autoCalcTableHeight();
});

onUnmounted(() => {
    window.removeEventListener('resize', autoCalcTableHeight);
});
```

## 参考页面

以下页面严格遵循此布局规范：
- 登录登出记录页面 (`/views/support/login-log/login-log-list.vue`)
- 任务发布页面 (`/views/business/task/task-list.vue`)
- 客户视角页面 (`/views/business/customer/customer-view.vue`)

## 注意事项

1. **不要使用固定定位的分页**: 分页组件应始终在卡片内部，不要使用 `position: fixed`
2. **保持样式类的一致性**: 严格使用系统定义的样式类，如 `smart-query-form`、`smart-query-table-page` 等
3. **表格操作行的简洁性**: 优先使用 `TableOperator` 组件，避免复杂的自定义操作按钮布局
4. **响应式设计**: 确保页面在不同屏幕尺寸下都能正常显示
5. **间距统一**: 使用系统预定义的间距类，如 `smart-margin-left10`、`smart-margin-bottom5` 等

## 完整模板

```vue
<template>
    <!-- 查询表单 -->
    <a-form class="smart-query-form" ref="queryFormRef">
        <a-row class="smart-query-form-row">
            <!-- 查询字段 -->
            <a-form-item label="字段名" class="smart-query-form-item">
                <a-input v-model:value="queryForm.field" placeholder="请输入" />
            </a-form-item>
            <!-- 操作按钮 -->
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="onSearch">查询</a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">重置</a-button>
            </a-form-item>
        </a-row>
    </a-form>

    <!-- 主要内容卡片 -->
    <a-card size="small" :bordered="false" :hoverable="true">
        <!-- 表格操作行 -->
        <a-row justify="end" ref="tableOperatorRef">
            <TableOperator 
                class="smart-margin-bottom5" 
                v-model="columns" 
                :tableId="TABLE_ID_CONST.XXX" 
                :refresh="queryData" 
            />
        </a-row>

        <!-- 数据表格 -->
        <a-table
            size="small"
            :dataSource="tableData"
            :columns="columns"
            rowKey="id"
            bordered
            :loading="tableLoading"
            :pagination="false"
            :scroll="{ y: scrollY }"
        >
            <!-- 表格内容模板 -->
        </a-table>

        <!-- 分页组件 -->
        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="queryForm.pageSize"
                v-model:current="queryForm.pageNum"
                v-model:pageSize="queryForm.pageSize"
                :total="total"
                @change="queryData"
                @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`"
            />
        </div>
    </a-card>
</template>

<script setup>
import { reactive, ref, onMounted, onUnmounted } from 'vue';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { calcTableHeight } from '/@/lib/table-auto-height';
import TableOperator from '/@/components/support/table-operator/index.vue';
import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';

// 表格高度自适应
const scrollY = ref(100);
const tableOperatorRef = ref();
const queryFormRef = ref();

function autoCalcTableHeight() {
    calcTableHeight(scrollY, [tableOperatorRef, queryFormRef], 10);
}

// 生命周期
onMounted(() => {
    queryData();
    autoCalcTableHeight();
});

onUnmounted(() => {
    window.removeEventListener('resize', autoCalcTableHeight);
});

window.addEventListener('resize', autoCalcTableHeight);
</script>
```

---

**版本**: v1.0  
**更新时间**: 2025-01-14  
**适用范围**: SmartAdmin 系统所有列表页面 