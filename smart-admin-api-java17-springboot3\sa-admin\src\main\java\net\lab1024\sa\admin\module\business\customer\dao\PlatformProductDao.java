package net.lab1024.sa.admin.module.business.customer.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.customer.domain.entity.PlatformProductEntity;
import net.lab1024.sa.admin.module.business.customer.domain.form.PlatformProductQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.PlatformProductVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 平台货品DAO
 *
 * @Author: 汪波
 * @Date: 2025-01-16 09:00:00
 * @Copyright: 1.0
 */
@Mapper
public interface PlatformProductDao extends BaseMapper<PlatformProductEntity> {
    
    /**
     * 查询平台货品列表(分页)
     */
    List<PlatformProductVO> queryPage(Page<PlatformProductVO> page, @Param("queryForm") PlatformProductQueryForm queryForm);
    
    /**
     * 获取所有店铺名称列表
     */
    List<String> getAllShopNames();
} 