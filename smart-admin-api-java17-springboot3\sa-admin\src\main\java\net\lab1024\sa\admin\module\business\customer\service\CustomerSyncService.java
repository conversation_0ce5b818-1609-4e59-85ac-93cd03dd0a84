package net.lab1024.sa.admin.module.business.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.customer.dao.CustomerViewDao;
import net.lab1024.sa.admin.module.business.customer.domain.entity.CustomerEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户数据同步服务
 * 从lirun数据库的crmk客户视角表同步数据到t_customer表
 *
 * <AUTHOR>
 * @Date 2025-01-09 10:00:00
 * @Copyright 1.0
 */
@Slf4j
@Service
public class CustomerSyncService {

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private CustomerViewDao customerViewDao;

    /**
     * 同步客户数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncCustomerData() {
        try {
            log.info("开始同步客户数据...");

            // 查询smart_admin_v3数据库的crmk客户视角表数据
            String sql = """
                SELECT
                    客户唯一编码,
                    总销售额,
                    总货品成本,
                    总商品毛利,
                    总商品毛利率,
                    累计次数,
                    复购周期,
                    首次成交日期,
                    上次购买日期,
                    流失风险,
                    上次购买距离今天数,
                    累计天数,
                    最近一次成交订单号,
                    最近一次成交店铺,
                    数据版本,
                    创建时间,
                    更新时间
                FROM `crmk客户视角`
                WHERE 1=1
                ORDER BY 更新时间 DESC
                """;

            List<CustomerEntity> customerList = jdbcTemplate.query(sql, new CustomerRowMapper());

            log.info("从crmk客户视角表查询到 {} 条客户数据", customerList.size());
            
            // 清空现有数据（可选，根据业务需求决定）
            // customerViewDao.delete(new QueryWrapper<CustomerEntity>().eq("deleted_flag", false));

            // 批量插入或更新数据
            int successCount = 0;
            int errorCount = 0;

            for (CustomerEntity customer : customerList) {
                try {
                    // 检查是否已存在
                    CustomerEntity existingCustomer = customerViewDao.selectOne(
                        new QueryWrapper<CustomerEntity>()
                            .eq("customer_unique_code", customer.getCustomerUniqueCode())
                            .eq("deleted_flag", false)
                    );

                    if (existingCustomer != null) {
                        // 更新现有记录
                        customer.setCustomerId(existingCustomer.getCustomerId());
                        customer.setCreateUserId(existingCustomer.getCreateUserId());
                        customer.setCreateTime(existingCustomer.getCreateTime());
                        customer.setUpdateUserId(1L); // 系统更新
                        customerViewDao.updateById(customer);
                    } else {
                        // 插入新记录
                        customer.setDeletedFlag(false);
                        customer.setCreateUserId(1L); // 系统创建
                        customer.setUpdateUserId(1L);
                        customerViewDao.insert(customer);
                    }
                    successCount++;
                } catch (Exception e) {
                    log.error("同步客户数据失败，客户编码: {}, 错误: {}", customer.getCustomerUniqueCode(), e.getMessage());
                    errorCount++;
                }
            }
            
            log.info("客户数据同步完成，成功: {} 条，失败: {} 条", successCount, errorCount);
            
        } catch (Exception e) {
            log.error("同步客户数据异常", e);
            throw new RuntimeException("同步客户数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 客户数据行映射器
     */
    private static class CustomerRowMapper implements RowMapper<CustomerEntity> {
        @Override
        public CustomerEntity mapRow(ResultSet rs, int rowNum) throws SQLException {
            CustomerEntity customer = new CustomerEntity();
            
            customer.setCustomerUniqueCode(rs.getString("客户唯一编码"));
            customer.setTotalSalesAmount(rs.getBigDecimal("总销售额"));
            customer.setTotalCostAmount(rs.getBigDecimal("总货品成本"));
            customer.setTotalProfitAmount(rs.getBigDecimal("总商品毛利"));
            customer.setProfitRate(rs.getString("总商品毛利率"));
            customer.setPurchaseCount(rs.getInt("累计次数"));
            
            // 处理可能为null的复购周期
            Object repurchaseCycleObj = rs.getObject("复购周期");
            if (repurchaseCycleObj != null) {
                customer.setRepurchaseCycle(rs.getInt("复购周期"));
            }
            
            // 处理日期字段
            java.sql.Date firstPurchaseDate = rs.getDate("首次成交日期");
            if (firstPurchaseDate != null) {
                customer.setFirstPurchaseDate(firstPurchaseDate.toLocalDate());
            }
            
            java.sql.Date lastPurchaseDate = rs.getDate("上次购买日期");
            if (lastPurchaseDate != null) {
                customer.setLastPurchaseDate(lastPurchaseDate.toLocalDate());
            }
            
            customer.setChurnRisk(rs.getString("流失风险"));
            customer.setDaysSinceLastPurchase(rs.getInt("上次购买距离今天数"));
            customer.setTotalDays(rs.getInt("累计天数"));
            customer.setLatestOrderNo(rs.getString("最近一次成交订单号"));
            customer.setLatestShopName(rs.getString("最近一次成交店铺"));
            customer.setDataVersion(rs.getString("数据版本"));
            
            // 处理时间戳字段
            java.sql.Timestamp createTime = rs.getTimestamp("创建时间");
            if (createTime != null) {
                customer.setCreateTime(createTime.toLocalDateTime());
            }
            
            java.sql.Timestamp updateTime = rs.getTimestamp("更新时间");
            if (updateTime != null) {
                customer.setUpdateTime(updateTime.toLocalDateTime());
            }
            
            return customer;
        }
    }
}
