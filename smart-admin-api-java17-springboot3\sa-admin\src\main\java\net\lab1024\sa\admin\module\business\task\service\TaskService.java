package net.lab1024.sa.admin.module.business.task.service;

import java.util.List;
import net.lab1024.sa.admin.module.business.task.dao.TaskDao;
import net.lab1024.sa.admin.module.business.task.domain.entity.TaskEntity;
import net.lab1024.sa.admin.module.business.task.domain.form.TaskAddForm;
import net.lab1024.sa.admin.module.business.task.domain.form.TaskQueryForm;
import net.lab1024.sa.admin.module.business.task.domain.form.TaskUpdateForm;
import net.lab1024.sa.admin.module.business.task.domain.vo.TaskVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 任务表 Service
 *
 * <AUTHOR>
 * @Date 2025-05-13 09:38:25
 * @Copyright 1.0
 */

@Service
public class TaskService {

    @Resource
    private TaskDao taskDao;

    /**
     * 分页查询
     */
    public PageResult<TaskVO> queryPage(TaskQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<TaskVO> list = taskDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(TaskAddForm addForm) {
        TaskEntity taskEntity = SmartBeanUtil.copy(addForm, TaskEntity.class);
        taskDao.insert(taskEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     */
    public ResponseDTO<String> update(TaskUpdateForm updateForm) {
        TaskEntity taskEntity = SmartBeanUtil.copy(updateForm, TaskEntity.class);
        taskDao.updateById(taskEntity);
        return ResponseDTO.ok();
    }

}
