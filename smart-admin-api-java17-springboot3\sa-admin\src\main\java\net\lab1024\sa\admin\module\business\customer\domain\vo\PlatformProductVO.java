package net.lab1024.sa.admin.module.business.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 平台货品VO
 *
 * @Author: 汪波
 * @Date: 2025-01-16 09:00:00
 * @Copyright: 1.0
 */
@Data
public class PlatformProductVO {
    
    @Schema(description = "货品ID")
    private String goodsId;
    
    @Schema(description = "店铺名称")
    private String shopName;
    
    @Schema(description = "显示名称 (货品ID - 店铺)")
    private String displayName;

    @Schema(description = "商家编码 (平台规格编码)")
    private String merchantCode;

    @Schema(description = "销量")
    private Integer salesVolume;
} 