package net.lab1024.sa.admin.module.business.task.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 任务表 实体类
 *
 * <AUTHOR>
 * @Date 2025-05-13 09:38:25
 * @Copyright 1.0
 */

@Data
@TableName("t_task")
public class TaskEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 分数
     */
    private Integer score;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 接收者
     */
    private String receiver;

    /**
     * 备注
     */
    private String remark;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布者
     */
    private String publisher;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
