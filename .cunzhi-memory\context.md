# 项目上下文信息

- SmartAdmin项目结构：
1. 项目类型：前后端分离的企业级管理系统
2. 前端：smart-admin-web-javascript (Vue3 + Ant Design Vue + Vite)
3. 后端：smart-admin-api-java17-springboot3 (Java17 + SpringBoot3 + Sa-Token + Mybatis-Plus)
4. 移动端：smart-app (UniApp + Vue3)
5. 数据库：sql目录包含数据库脚本
6. 技术栈：满足三级等保要求，支持加解密、数据脱敏等安全功能
- 客户管理模块详细结构：
1. 前端页面：customer-view.vue（主页面）、customer-link.vue（客户关联）
2. 前端组件：customer-view-detail-modal.vue、customer-view-statistics-modal.vue、customer-record-modal.vue等
3. 前端API：customer-view-api.js、customer-order-api.js、customer-link-api.js、customer-record-api.js
4. 后端Controller：CustomerViewController、CustomerOrderController、CustomerLinkController、CustomerRecordController
5. 后端Service：CustomerViewService、CustomerOrderService、CustomerLinkService、CustomerRecordService、LirunJdbcService
6. 数据实体：CustomerEntity、CustomerLinkEntity、CustomerRecordEntity、PlatformProductEntity
7. 权限标识：customer:view:query、customer:view:detail、customer:view:export、customer:record:manage
- 链接视角功能详细结构：前端页面customer-link.vue，后端Controller/Service/DAO，数据库表lirun.订单明细和smart_admin_v3.t_menu，菜单ID 3074，权限包括查询、统计分析、下载明细等
- SQL文件夹已完成重新整理：1)创建了4个分类目录：01-database-init(数据库初始化)、02-version-updates(版本更新)、03-feature-scripts(功能脚本)、04-deprecated(已废弃)；2)将customer_classification_enhancement.sql移至废弃目录，因为已改为实时计算方案；3)整理了版本更新脚本、功能脚本和代码生成模板；4)创建了详细的README文档说明各脚本用途和使用方法
- 用户希望为员工管理系统添加高级安全验证功能，当执行敏感操作（禁用/启用员工、重置密码）时需要向超级管理员邮箱发送验证。需要分析技术可行性、架构影响和实现方案。
- 链接视角页面复购明细表格新增客单价字段：客单价=复购子订单付款金额÷付款人数，位置在复购金额右侧，保留2位小数，货币格式，支持排序
- 链接视角页面复购明细列表功能修改：在前端Vue组件中过滤掉第0次购买记录，只显示第1次及以后的复购记录。修改位置：customer-link.vue第997-1000行，在数据排序前增加过滤逻辑filteredData = rawData.filter(item => item.repurchaseTimes !== '第0次')，确保复购明细表格不显示非复购客户数据
- 链接视角页面复购明细标题增强：在复购明细标题旁边新增统计信息显示，包含"目标客户人数 xxx 人，其中复购人数 xx 人，复购率为 x.xx%"。修改位置：customer-link.vue第242-257行HTML结构调整，第955-966行新增formatRepurchaseRate()方法，第1516-1541行新增CSS样式。复购率计算逻辑：复购人数/付款人数*100%，保留2位小数
- 链接视角页面复购明细统计信息样式优化：数字部分使用橙色高亮显示(#ff7a00)，字体加粗(font-weight: 600)，字号14px。整体文字调整为13px，行高1.5，增加2px上边距。HTML结构中为数字部分添加number-highlight类名
- 链接视角页面目标客户人数修正：新增targetCustomerCount字段，基于付款日期范围计算，不受回购日期影响。修改位置：1)CustomerLinkAnalysisVO.java第24-25行新增字段；2)CustomerLinkMapper.xml第209行新增子查询；3)customer-link.vue第246行使用targetCustomerCount，第956-967行修改复购率计算逻辑使用targetCustomerCount作为分母
- 链接视角页面复购率分析区域付款人数修正：将复购率分析区域的付款人数显示也修改为使用targetCustomerCount，确保不受回购日期影响。修改位置：customer-link.vue第224行，统一使用targetCustomerCount || paymentUserCount的兼容逻辑
- 链接视角页面复购人数逻辑修正：新增targetRepurchaseUserCount字段，基于付款日期范围计算复购人数，不受回购日期影响。修改位置：1)CustomerLinkAnalysisVO.java第30-31行新增字段；2)CustomerLinkMapper.xml第211行新增子查询；3)customer-link.vue第214行复购率分析区域、第247行复购明细标题区域、第965行复购率计算方法，统一使用targetRepurchaseUserCount优先逻辑
