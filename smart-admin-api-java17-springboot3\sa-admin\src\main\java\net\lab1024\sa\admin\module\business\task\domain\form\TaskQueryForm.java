package net.lab1024.sa.admin.module.business.task.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.base.common.json.deserializer.DictDataDeserializer;

/**
 * 任务表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-05-13 09:38:25
 * @Copyright 1.0
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class TaskQueryForm extends PageParam {

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "发布者")
    private String publisher;

    @Schema(description = "接收者")
    private String receiver;

    @Schema(description = "任务状态")
    @JsonDeserialize(using = DictDataDeserializer.class)
    private String status;

    @Schema(description = "发布时间")
    private LocalDate publishTimeBegin;

    @Schema(description = "发布时间")
    private LocalDate publishTimeEnd;

}
