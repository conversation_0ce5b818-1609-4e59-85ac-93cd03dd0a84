package net.lab1024.sa.admin.module.business.customer.task;

import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.customer.service.CustomerSyncService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 客户数据同步定时任务
 *
 * <AUTHOR>
 * @Date 2025-01-09 10:00:00
 * @Copyright 1.0
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "customer.sync.enabled", havingValue = "true", matchIfMissing = false)
public class CustomerSyncTask {

    @Resource
    private CustomerSyncService customerSyncService;

    /**
     * 定时同步客户数据
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncCustomerDataTask() {
        log.info("开始执行定时客户数据同步任务...");
        try {
            customerSyncService.syncCustomerData();
            log.info("定时客户数据同步任务执行成功");
        } catch (Exception e) {
            log.error("定时客户数据同步任务执行失败", e);
        }
    }

    /**
     * 每小时同步一次（可选，根据业务需求开启）
     */
    @Scheduled(cron = "0 0 * * * ?")
    @ConditionalOnProperty(name = "customer.sync.hourly.enabled", havingValue = "true", matchIfMissing = false)
    public void syncCustomerDataHourly() {
        log.info("开始执行每小时客户数据同步任务...");
        try {
            customerSyncService.syncCustomerData();
            log.info("每小时客户数据同步任务执行成功");
        } catch (Exception e) {
            log.error("每小时客户数据同步任务执行失败", e);
        }
    }
}
