package net.lab1024.sa.admin.module.business.task.domain.form;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.base.common.json.deserializer.DictDataDeserializer;

/**
 * 任务表 新建表单
 *
 * <AUTHOR>
 * @Date 2025-05-13 09:38:25
 * @Copyright 1.0
 */

@Data
public class TaskAddForm {

    @Schema(description = "主键ID")
    private Long taskId;

    @Schema(description = "任务名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "任务名称 不能为空")
    private String taskName;

    @Schema(description = "分数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分数 不能为空")
    private Integer score;

    @Schema(description = "接收者", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "接收者 不能为空")
    private String receiver;

    @Schema(description = "发布者", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "发布者 不能为空")
    private String publisher;

}