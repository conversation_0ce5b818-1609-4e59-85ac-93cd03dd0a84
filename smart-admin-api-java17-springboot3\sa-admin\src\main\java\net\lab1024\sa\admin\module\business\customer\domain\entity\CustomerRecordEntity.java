package net.lab1024.sa.admin.module.business.customer.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 客户跟进记录实体类
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:00:00
 * @Copyright 1.0
 */
@Data
@TableName("t_customer_record")
public class CustomerRecordEntity {

    /**
     * 跟进记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long recordId;

    /**
     * 客户唯一编码
     */
    private String customerUniqueCode;

    /**
     * 跟进类型：phone-电话沟通,email-邮件联系,visit-上门拜访,wechat-微信沟通,problem-问题处理,other-其他
     */
    private String type;

    /**
     * 跟进状态：pending-待跟进,processing-跟进中,completed-已完成,cancelled-已取消
     */
    private String status;

    /**
     * 跟进时间
     */
    private LocalDateTime followTime;

    /**
     * 下次跟进时间
     */
    private LocalDateTime nextFollowTime;

    /**
     * 跟进内容
     */
    private String content;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除状态：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 