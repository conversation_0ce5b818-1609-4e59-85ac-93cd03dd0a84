package net.lab1024.sa.admin.module.business.task.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 任务表 列表VO
 *
 * <AUTHOR>
 * @Date 2025-05-13 09:38:25
 * @Copyright 1.0
 */

@Data
public class TaskVO {


    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "分数")
    private Integer score;

    @Schema(description = "任务状态")
    private String status;

    @Schema(description = "接收者")
    private String receiver;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "发布者")
    private String publisher;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
