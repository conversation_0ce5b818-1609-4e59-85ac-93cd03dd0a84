package net.lab1024.sa.admin.module.business.customer.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerOrderQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerOrderVO;
import net.lab1024.sa.admin.module.business.customer.service.CustomerOrderService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 客户订单明细控制器
 *
 * <AUTHOR>
 * @Date 2025-01-15 20:00:00
 * @Copyright 1.0
 */
@Tag(name = "客户订单明细")
@RestController
@OperateLog
public class CustomerOrderController {

    @Autowired
    private CustomerOrderService customerOrderService;

    @Operation(summary = "分页查询客户订单明细 <AUTHOR>
    @PostMapping("/api/customer/order/page/query")
    @SaCheckPermission("customer:order:view")
    public ResponseDTO<PageResult<CustomerOrderVO>> queryPage(@RequestBody @Valid CustomerOrderQueryForm queryForm) {
        return ResponseDTO.ok(customerOrderService.queryPage(queryForm));
    }

    @Operation(summary = "根据客户编码查询订单明细 <AUTHOR>
    @GetMapping("/api/customer/order/customer/{customerCode}")
    @SaCheckPermission("customer:order:view")
    public ResponseDTO<List<CustomerOrderVO>> queryByCustomerCode(@PathVariable String customerCode) {
        return ResponseDTO.ok(customerOrderService.queryByCustomerCode(customerCode));
    }
} 