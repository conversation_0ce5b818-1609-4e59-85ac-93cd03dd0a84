package net.lab1024.sa.admin.module.business.customer.service;

import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerOrderQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerOrderVO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户订单明细服务
 *
 * <AUTHOR>
 * @Date 2025-01-15 20:00:00
 * @Copyright 1.0
 */
@Service
public class CustomerOrderService {

    @Autowired
    private LirunJdbcService lirunJdbcService;

    /**
     * 分页查询客户订单明细
     */
    public PageResult<CustomerOrderVO> queryPage(CustomerOrderQueryForm queryForm) {
        return lirunJdbcService.queryOrderPage(queryForm);
    }

    /**
     * 根据客户编码查询订单明细
     */
    public List<CustomerOrderVO> queryByCustomerCode(String customerCode) {
        return lirunJdbcService.queryByCustomerCode(customerCode);
    }
} 