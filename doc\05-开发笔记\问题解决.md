# SmartAdmin 问题解决手册

## 📋 概述

本文档收集了 SmartAdmin 项目开发和运维过程中遇到的常见问题及其解决方案，帮助团队快速定位和解决问题。

## 🗄️ 数据库相关问题

### 连接问题

#### 1. 权限拒绝错误
**错误信息**：
```
Access denied for user 'root'@'*************' (using password: YES)
```

**可能原因**：
- 数据库用户没有远程连接权限
- 密码错误
- IP 地址不在白名单中

**解决步骤**：
1. 检查用户权限：
```sql
SELECT User, Host FROM mysql.user WHERE User = 'root';
```

2. 授予远程连接权限：
```sql
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' IDENTIFIED BY 'password';
FLUSH PRIVILEGES;
```

3. 检查防火墙设置：
```bash
# CentOS/RHEL
firewall-cmd --permanent --add-port=3306/tcp
firewall-cmd --reload

# Ubuntu
ufw allow 3306
```

#### 2. 公钥检索错误
**错误信息**：
```
Public Key Retrieval is not allowed
```

**解决方案**：
在数据库连接 URL 中添加参数：
```yaml
spring:
  datasource:
    url: ************************************************************
```

#### 3. 时区错误
**错误信息**：
```
The server time zone value 'CST' is unrecognized
```

**解决方案**：
在连接 URL 中指定时区：
```yaml
spring:
  datasource:
    url: ************************************************************
```

### 字符集问题

#### 1. 字符集冲突
**错误信息**：
```
Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT)
```

**解决方案**：
使用 CONVERT 和 COLLATE 函数统一字符集：
```sql
SELECT * FROM table1 t1
JOIN table2 t2 ON CONVERT(t1.code USING utf8mb4) COLLATE utf8mb4_general_ci = 
                  CONVERT(t2.code USING utf8mb4) COLLATE utf8mb4_general_ci;
```

#### 2. 中文乱码
**问题表现**：数据库中中文显示为乱码

**解决方案**：
1. 检查数据库字符集：
```sql
SHOW VARIABLES LIKE 'character_set%';
```

2. 修改数据库字符集：
```sql
ALTER DATABASE database_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. 修改表字符集：
```sql
ALTER TABLE table_name CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 性能问题

#### 1. 查询缓慢
**问题表现**：SQL 查询执行时间过长

**排查步骤**：
1. 使用 EXPLAIN 分析查询计划：
```sql
EXPLAIN SELECT * FROM table WHERE condition;
```

2. 检查索引使用情况：
```sql
SHOW INDEX FROM table_name;
```

3. 查看慢查询日志：
```sql
SHOW VARIABLES LIKE 'slow_query_log%';
```

**优化方案**：
- 添加合适的索引
- 优化 WHERE 条件
- 使用 LIMIT 限制结果集
- 考虑分页查询

#### 2. 连接池耗尽
**错误信息**：
```
Could not get JDBC Connection; nested exception is java.sql.SQLException: Connections could not be acquired from the underlying database!
```

**解决方案**：
调整连接池配置：
```yaml
spring:
  datasource:
    initial-size: 5
    min-idle: 5
    max-active: 20
    max-wait: 60000
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 300000
```

## 🎯 前端相关问题

### Vue3 组件问题

#### 1. 响应式数据失效
**问题表现**：数据更新但页面不刷新

**可能原因**：
- 直接修改数组索引
- 对象属性动态添加
- 使用了非响应式的数据

**解决方案**：
```javascript
// 错误做法
data.list[0] = newValue;

// 正确做法
data.list.splice(0, 1, newValue);
// 或使用 ref/reactive
const list = ref([]);
list.value[0] = newValue;
```

#### 2. API 方法不存在
**错误信息**：
```
TypeError: api.methodName is not a function
```

**排查步骤**：
1. 检查 API 文件中的实际方法名
2. 确认 import 语句正确
3. 检查方法是否正确导出

**解决方案**：
```javascript
// 检查 API 文件
export const userApi = {
  queryAll: () => request.get('/api/user/all'),
  // 确保方法名正确
};

// 正确导入和使用
import { userApi } from '/@/api/user-api';
const result = await userApi.queryAll();
```

#### 3. 表单验证失败
**问题表现**：表单提交时验证不通过

**常见原因**：
- 验证规则配置错误
- 表单字段名不匹配
- 异步验证处理不当

**解决方案**：
```javascript
// 确保表单字段名与验证规则匹配
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ]
};

// 表单提交前验证
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    // 验证通过，提交表单
  } catch (error) {
    console.log('验证失败:', error);
  }
};
```

### 路由问题

#### 1. 路由跳转失败
**问题表现**：点击菜单或按钮无法跳转页面

**排查步骤**：
1. 检查路由配置是否正确
2. 确认用户是否有访问权限
3. 检查路由守卫逻辑

**解决方案**：
```javascript
// 检查路由配置
{
  path: '/business/customer/view',
  name: 'CustomerView',
  component: () => import('/@/views/business/customer/customer-view.vue'),
  meta: {
    title: '客户视角',
    requiresAuth: true
  }
}
```

#### 2. 权限控制失效
**问题表现**：用户能访问无权限的页面

**解决方案**：
```javascript
// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore();
  
  if (to.meta.requiresAuth && !userStore.hasPermission(to.meta.permission)) {
    next('/403');
  } else {
    next();
  }
});
```

## 🔧 后端相关问题

### Spring Boot 问题

#### 1. Bean 注入失败
**错误信息**：
```
No qualifying bean of type 'com.example.Service' available
```

**可能原因**：
- 类没有添加 @Component 等注解
- 包扫描路径不正确
- 循环依赖

**解决方案**：
```java
// 确保添加正确注解
@Service
public class UserService {
    // ...
}

// 检查主类的包扫描配置
@SpringBootApplication(scanBasePackages = "com.example")
public class Application {
    // ...
}
```

#### 2. 配置文件不生效
**问题表现**：修改配置文件后应用行为没有改变

**排查步骤**：
1. 检查配置文件路径是否正确
2. 确认环境配置是否匹配
3. 检查是否有多个配置文件冲突

**解决方案**：
- 确保配置文件在正确的 resources 目录下
- 使用 @Value 注解时检查属性名是否正确
- 检查 application.yml 中的 profiles.active 设置

#### 3. 事务不生效
**问题表现**：数据库操作没有回滚

**可能原因**：
- 方法不是 public
- 在同一个类中调用事务方法
- 异常被捕获但没有抛出

**解决方案**：
```java
@Service
@Transactional
public class UserService {
    
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(User user) {
        // 确保方法是 public
        // 异常要抛出，不能被捕获
        userDao.update(user);
    }
}
```

## 🚀 部署相关问题

### Docker 部署问题

#### 1. 容器启动失败
**错误信息**：
```
Container exited with code 1
```

**排查步骤**：
1. 查看容器日志：
```bash
docker logs container_name
```

2. 检查 Dockerfile 配置
3. 验证环境变量设置

**常见解决方案**：
- 检查 Java 版本兼容性
- 确认配置文件路径正确
- 验证数据库连接配置

#### 2. 端口访问问题
**问题表现**：外部无法访问容器服务

**解决方案**：
```bash
# 检查端口映射
docker ps

# 正确的端口映射
docker run -p 8080:8080 app_image

# 检查防火墙设置
firewall-cmd --list-ports
```

### Nginx 配置问题

#### 1. 静态资源 404
**问题表现**：前端页面无法加载 CSS、JS 文件

**解决方案**：
```nginx
server {
    listen 80;
    server_name localhost;
    
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://backend:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 2. 跨域问题
**错误信息**：
```
Access to XMLHttpRequest at 'http://api.example.com' from origin 'http://localhost:3000' has been blocked by CORS policy
```

**解决方案**：
```nginx
location /api/ {
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    
    if ($request_method = 'OPTIONS') {
        return 204;
    }
    
    proxy_pass http://backend:8080/;
}
```

## 📊 监控和日志问题

### 日志问题

#### 1. 日志不输出
**可能原因**：
- 日志级别配置过高
- 日志文件权限问题
- 日志配置文件错误

**解决方案**：
```yaml
logging:
  level:
    com.example: DEBUG
    org.springframework: INFO
  file:
    name: /var/log/app.log
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

#### 2. 日志文件过大
**解决方案**：
配置日志轮转：
```yaml
logging:
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30
      total-size-cap: 1GB
```

## 🔍 问题排查工具

### 常用命令
```bash
# 查看系统资源使用情况
top
htop

# 查看端口占用
netstat -tlnp
ss -tlnp

# 查看进程
ps aux | grep java

# 查看磁盘使用情况
df -h
du -sh /path/to/directory

# 查看内存使用情况
free -h
```

### 日志分析
```bash
# 查看实时日志
tail -f /var/log/app.log

# 搜索错误日志
grep -i error /var/log/app.log

# 统计错误数量
grep -c "ERROR" /var/log/app.log
```

---

💡 **提示**：遇到问题时，首先查看日志输出，然后根据错误信息进行针对性排查。建议建立问题处理流程，及时记录和分享解决方案。
