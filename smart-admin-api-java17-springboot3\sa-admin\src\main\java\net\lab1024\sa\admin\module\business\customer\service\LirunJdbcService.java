package net.lab1024.sa.admin.module.business.customer.service;

import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerOrderQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerViewQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerOrderVO;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerViewVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Lirun数据库JDBC服务
 *
 * <AUTHOR>
 * @Date 2025-01-15 21:30:00
 * @Copyright 1.0
 */
@Slf4j
@Service
public class LirunJdbcService {

    @Value("${lirun.datasource.url}")
    private String jdbcUrl;
    @Value("${lirun.datasource.username}")
    private String username;
    @Value("${lirun.datasource.password}")
    private String password;

    // ==================== 订单明细相关方法 ====================

    /**
     * 分页查询客户订单明细
     */
    public PageResult<CustomerOrderVO> queryOrderPage(CustomerOrderQueryForm queryForm) {
        List<CustomerOrderVO> list = new ArrayList<>();
        long total = 0;

        try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password)) {
            
            // 构建查询SQL
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT ");
            sql.append("原始单号 as originalOrderNo, ");
            sql.append("商家编码 as merchantCode, ");
            sql.append("数量 as quantity, ");
            sql.append("已付 as paidAmount, ");
            sql.append("分摊后总价 as orderAmount, ");
            sql.append("付款时间 as paymentTime, ");
            sql.append("客户唯一编码 as customerUniqueCode, ");
            sql.append("订单状态 as orderStatus, ");
            sql.append("客服备注 as customerServiceRemark ");
            sql.append("FROM 订单明细 ");
            sql.append("WHERE 1=1 ");

            List<Object> params = new ArrayList<>();
            
            if (queryForm.getCustomerUniqueCode() != null && !queryForm.getCustomerUniqueCode().trim().isEmpty()) {
                sql.append("AND 客户唯一编码 = ? ");
                params.add(queryForm.getCustomerUniqueCode());
            }
            
            if (queryForm.getPaymentTimeBegin() != null && !queryForm.getPaymentTimeBegin().trim().isEmpty()) {
                sql.append("AND 付款时间 >= ? ");
                params.add(queryForm.getPaymentTimeBegin());
            }
            
            if (queryForm.getPaymentTimeEnd() != null && !queryForm.getPaymentTimeEnd().trim().isEmpty()) {
                sql.append("AND 付款时间 <= ? ");
                params.add(queryForm.getPaymentTimeEnd());
            }

            sql.append("ORDER BY 付款时间 DESC ");

            // 获取总记录数
            String countSql = "SELECT COUNT(*) " + sql.substring(sql.indexOf("FROM"));
            countSql = countSql.replace("ORDER BY 付款时间 DESC", "");
            
            try (PreparedStatement countStmt = connection.prepareStatement(countSql)) {
                for (int i = 0; i < params.size(); i++) {
                    countStmt.setObject(i + 1, params.get(i));
                }
                ResultSet countRs = countStmt.executeQuery();
                if (countRs.next()) {
                    total = countRs.getLong(1);
                }
            }

            // 分页查询
            long offset = ((long) queryForm.getPageNum() - 1) * queryForm.getPageSize();
            sql.append("LIMIT ?, ?");
            params.add(offset);
            params.add((long) queryForm.getPageSize());

            try (PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
                for (int i = 0; i < params.size(); i++) {
                    stmt.setObject(i + 1, params.get(i));
                }

                ResultSet rs = stmt.executeQuery();
                while (rs.next()) {
                    CustomerOrderVO order = new CustomerOrderVO();
                    order.setOriginalOrderNo(rs.getString("originalOrderNo"));
                    order.setMerchantCode(rs.getString("merchantCode"));
                    order.setQuantity(rs.getInt("quantity"));
                    order.setPaidAmount(rs.getBigDecimal("paidAmount"));
                    order.setOrderAmount(rs.getBigDecimal("orderAmount"));
                    order.setPaymentTime(rs.getString("paymentTime"));
                    order.setCustomerUniqueCode(rs.getString("customerUniqueCode"));
                    order.setOrderStatus(rs.getString("orderStatus"));
                    order.setCustomerServiceRemark(rs.getString("customerServiceRemark"));
                    list.add(order);
                }
            }

        } catch (SQLException e) {
            log.error("查询订单明细失败", e);
            throw new RuntimeException("查询订单明细失败: " + e.getMessage());
        }

        PageResult<CustomerOrderVO> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal(total);
        pageResult.setPageNum((long) queryForm.getPageNum());
        pageResult.setPageSize((long) queryForm.getPageSize());
        
        return pageResult;
    }

    /**
     * 根据客户编码查询订单明细
     */
    public List<CustomerOrderVO> queryByCustomerCode(String customerCode) {
        List<CustomerOrderVO> list = new ArrayList<>();

        try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password)) {
            
            String sql = "SELECT " +
                    "原始单号 as originalOrderNo, " +
                    "商家编码 as merchantCode, " +
                    "数量 as quantity, " +
                    "已付 as paidAmount, " +
                    "分摊后总价 as orderAmount, " +
                    "付款时间 as paymentTime, " +
                    "客户唯一编码 as customerUniqueCode, " +
                    "订单状态 as orderStatus, " +
                    "客服备注 as customerServiceRemark " +
                    "FROM 订单明细 " +
                    "WHERE 客户唯一编码 = ? " +
                    "ORDER BY 付款时间 DESC " +
                    "LIMIT 100";

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, customerCode);
                ResultSet rs = stmt.executeQuery();
                
                while (rs.next()) {
                    CustomerOrderVO order = new CustomerOrderVO();
                    order.setOriginalOrderNo(rs.getString("originalOrderNo"));
                    order.setMerchantCode(rs.getString("merchantCode"));
                    order.setQuantity(rs.getInt("quantity"));
                    order.setPaidAmount(rs.getBigDecimal("paidAmount"));
                    order.setOrderAmount(rs.getBigDecimal("orderAmount"));
                    order.setPaymentTime(rs.getString("paymentTime"));
                    order.setCustomerUniqueCode(rs.getString("customerUniqueCode"));
                    order.setOrderStatus(rs.getString("orderStatus"));
                    order.setCustomerServiceRemark(rs.getString("customerServiceRemark"));
                    list.add(order);
                }
            }

        } catch (SQLException e) {
            log.error("根据客户编码查询订单明细失败", e);
            throw new RuntimeException("查询订单明细失败: " + e.getMessage());
        }

        return list;
    }

    // ==================== 客户视角相关方法 ====================

    /**
     * 分页查询客户视角数据
     */
    public ResponseDTO<PageResult<CustomerViewVO>> queryCustomerViewPage(CustomerViewQueryForm queryForm) {
        List<CustomerViewVO> list = new ArrayList<>();
        long total = 0;

        try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password)) {
            
            // 构建查询SQL
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT ");
            sql.append("客户唯一编码, ");
            sql.append("总销售额, ");
            sql.append("总成本, ");
            sql.append("总利润, ");
            sql.append("总客户毛利率, ");
            sql.append("购买次数, ");
            sql.append("复购周期, ");
            sql.append("首次成交日期, ");
            sql.append("上次购买日期, ");
            sql.append("流失风险, ");
            sql.append("上次购买后距今天数, ");
            sql.append("购买天数, ");
            sql.append("最近一次下单店铺, ");
            sql.append("最近下单原始单号, ");
            sql.append("会员等级 ");
            sql.append("FROM crm_客户查询 ");
            sql.append("WHERE 1=1 ");

            List<Object> params = new ArrayList<>();
            
            // 添加查询条件
            if (queryForm.getCustomerUniqueCode() != null && !queryForm.getCustomerUniqueCode().trim().isEmpty()) {
                sql.append("AND 客户唯一编码 LIKE ? ");
                params.add("%" + queryForm.getCustomerUniqueCode() + "%");
            }
            
            if (queryForm.getLatestOrderNo() != null && !queryForm.getLatestOrderNo().trim().isEmpty()) {
                sql.append("AND 最近下单原始单号 LIKE ? ");
                params.add("%" + queryForm.getLatestOrderNo() + "%");
            }
            
            if (queryForm.getLatestShopName() != null && !queryForm.getLatestShopName().trim().isEmpty()) {
                sql.append("AND 最近一次下单店铺 LIKE ? ");
                params.add("%" + queryForm.getLatestShopName() + "%");
            }

            if (queryForm.getChurnRisk() != null && !queryForm.getChurnRisk().trim().isEmpty() && !"NULL".equals(queryForm.getChurnRisk())) {
                sql.append("AND 流失风险 = ? ");
                params.add(queryForm.getChurnRisk());
            }

            if (queryForm.getMemberLevel() != null && !queryForm.getMemberLevel().trim().isEmpty() && !"NULL".equals(queryForm.getMemberLevel())) {
                sql.append("AND 会员等级 = ? ");
                params.add(queryForm.getMemberLevel());
            }

            if (queryForm.getSalesAmountMin() != null) {
                sql.append("AND 总销售额 >= ? ");
                params.add(queryForm.getSalesAmountMin());
            }

            if (queryForm.getSalesAmountMax() != null) {
                sql.append("AND 总销售额 <= ? ");
                params.add(queryForm.getSalesAmountMax());
            }

            if (queryForm.getPurchaseCountMin() != null) {
                sql.append("AND 购买次数 >= ? ");
                params.add(queryForm.getPurchaseCountMin());
            }

            if (queryForm.getPurchaseCountMax() != null) {
                sql.append("AND 购买次数 <= ? ");
                params.add(queryForm.getPurchaseCountMax());
            }

            if (queryForm.getFirstPurchaseDateBegin() != null) {
                sql.append("AND 首次成交日期 >= ? ");
                params.add(queryForm.getFirstPurchaseDateBegin());
            }

            if (queryForm.getFirstPurchaseDateEnd() != null) {
                sql.append("AND 首次成交日期 <= ? ");
                params.add(queryForm.getFirstPurchaseDateEnd());
            }

            if (queryForm.getLastPurchaseDateBegin() != null) {
                sql.append("AND 上次购买日期 >= ? ");
                params.add(queryForm.getLastPurchaseDateBegin());
            }
            
            if (queryForm.getLastPurchaseDateEnd() != null) {
                sql.append("AND 上次购买日期 <= ? ");
                params.add(queryForm.getLastPurchaseDateEnd());
            }

            sql.append("ORDER BY 上次购买日期 DESC ");

            // 获取总记录数
            String countSql = "SELECT COUNT(*) " + sql.substring(sql.indexOf("FROM"));
            countSql = countSql.replace("ORDER BY 上次购买日期 DESC", "");
            
            try (PreparedStatement countStmt = connection.prepareStatement(countSql)) {
                for (int i = 0; i < params.size(); i++) {
                    countStmt.setObject(i + 1, params.get(i));
                }
                ResultSet countRs = countStmt.executeQuery();
                if (countRs.next()) {
                    total = countRs.getLong(1);
                }
            }

            // 分页查询
            long offset = ((long) queryForm.getPageNum() - 1) * queryForm.getPageSize();
            sql.append("LIMIT ?, ?");
            params.add(offset);
            params.add((long) queryForm.getPageSize());

            try (PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
                for (int i = 0; i < params.size(); i++) {
                    stmt.setObject(i + 1, params.get(i));
                }

                ResultSet rs = stmt.executeQuery();
                while (rs.next()) {
                    CustomerViewVO customer = new CustomerViewVO();
                    customer.set客户唯一编码(rs.getString("客户唯一编码"));
                    customer.set总销售额(rs.getBigDecimal("总销售额"));
                    customer.set总成本(rs.getBigDecimal("总成本"));
                    customer.set总利润(rs.getBigDecimal("总利润"));
                    customer.set总客户毛利率(rs.getString("总客户毛利率"));
                    customer.set购买次数(rs.getInt("购买次数"));
                    customer.set复购周期(rs.getInt("复购周期"));
                    
                    Date firstPurchaseDate = rs.getDate("首次成交日期");
                    if (firstPurchaseDate != null) {
                        customer.set首次成交日期(firstPurchaseDate.toLocalDate());
                    }
                    
                    Date lastPurchaseDate = rs.getDate("上次购买日期");
                    if (lastPurchaseDate != null) {
                        customer.set上次购买日期(lastPurchaseDate.toLocalDate());
                    }
                    
                    customer.set流失风险(rs.getString("流失风险"));
                    customer.set上次购买后距今天数(rs.getInt("上次购买后距今天数"));
                    customer.set购买天数(rs.getInt("购买天数"));
                    customer.set最近一次下单店铺(rs.getString("最近一次下单店铺"));
                    customer.set最近下单原始单号(rs.getString("最近下单原始单号"));
                    customer.set会员等级(rs.getString("会员等级"));
                    
                    list.add(customer);
                }
            }

        } catch (SQLException e) {
            log.error("查询客户视角数据失败", e);
            throw new RuntimeException("查询客户视角数据失败: " + e.getMessage());
        }

        PageResult<CustomerViewVO> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal(total);
        pageResult.setPageNum((long) queryForm.getPageNum());
        pageResult.setPageSize((long) queryForm.getPageSize());
        
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 查询客户详情
     */
    public ResponseDTO<CustomerViewVO> getCustomerViewDetail(String customerUniqueCode) {
        try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password)) {
            
            String sql = "SELECT " +
                    "客户唯一编码, " +
                    "总销售额, " +
                    "总成本, " +
                    "总利润, " +
                    "总客户毛利率, " +
                    "购买次数, " +
                    "复购周期, " +
                    "首次成交日期, " +
                    "上次购买日期, " +
                    "流失风险, " +
                    "上次购买后距今天数, " +
                    "购买天数, " +
                    "最近一次下单店铺, " +
                    "最近下单原始单号, " +
                    "会员等级 " +
                    "FROM crm_客户查询 " +
                    "WHERE 客户唯一编码 = ?";

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, customerUniqueCode);
                ResultSet rs = stmt.executeQuery();
                
                if (rs.next()) {
                    CustomerViewVO customer = new CustomerViewVO();
                    customer.set客户唯一编码(rs.getString("客户唯一编码"));
                    customer.set总销售额(rs.getBigDecimal("总销售额"));
                    customer.set总成本(rs.getBigDecimal("总成本"));
                    customer.set总利润(rs.getBigDecimal("总利润"));
                    customer.set总客户毛利率(rs.getString("总客户毛利率"));
                    customer.set购买次数(rs.getInt("购买次数"));
                    customer.set复购周期(rs.getInt("复购周期"));
                    
                    Date firstPurchaseDate = rs.getDate("首次成交日期");
                    if (firstPurchaseDate != null) {
                        customer.set首次成交日期(firstPurchaseDate.toLocalDate());
                    }
                    
                    Date lastPurchaseDate = rs.getDate("上次购买日期");
                    if (lastPurchaseDate != null) {
                        customer.set上次购买日期(lastPurchaseDate.toLocalDate());
                    }
                    
                    customer.set流失风险(rs.getString("流失风险"));
                    customer.set上次购买后距今天数(rs.getInt("上次购买后距今天数"));
                    customer.set购买天数(rs.getInt("购买天数"));
                    customer.set最近一次下单店铺(rs.getString("最近一次下单店铺"));
                    customer.set最近下单原始单号(rs.getString("最近下单原始单号"));
                    customer.set会员等级(rs.getString("会员等级"));
                    
                    return ResponseDTO.ok(customer);
                } else {
                    return ResponseDTO.userErrorParam("客户不存在");
                }
            }

        } catch (SQLException e) {
            log.error("查询客户详情失败", e);
            throw new RuntimeException("查询客户详情失败: " + e.getMessage());
        }
    }

    /**
     * 导出客户视角数据
     */
    public List<CustomerViewVO> exportCustomerViewQuery(CustomerViewQueryForm queryForm) {
        List<CustomerViewVO> list = new ArrayList<>();

        try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password)) {
            
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT ");
            sql.append("客户唯一编码, ");
            sql.append("总销售额, ");
            sql.append("总成本, ");
            sql.append("总利润, ");
            sql.append("总客户毛利率, ");
            sql.append("购买次数, ");
            sql.append("复购周期, ");
            sql.append("首次成交日期, ");
            sql.append("上次购买日期, ");
            sql.append("流失风险, ");
            sql.append("上次购买后距今天数, ");
            sql.append("购买天数, ");
            sql.append("最近一次下单店铺, ");
            sql.append("最近下单原始单号, ");
            sql.append("会员等级 ");
            sql.append("FROM crm_客户查询 ");
            sql.append("WHERE 1=1 ");

            List<Object> params = new ArrayList<>();
            
            // 添加查询条件
            if (queryForm.getCustomerUniqueCode() != null && !queryForm.getCustomerUniqueCode().trim().isEmpty()) {
                sql.append("AND 客户唯一编码 LIKE ? ");
                params.add("%" + queryForm.getCustomerUniqueCode() + "%");
            }
            
            if (queryForm.getLatestOrderNo() != null && !queryForm.getLatestOrderNo().trim().isEmpty()) {
                sql.append("AND 最近下单原始单号 LIKE ? ");
                params.add("%" + queryForm.getLatestOrderNo() + "%");
            }
            
            if (queryForm.getLatestShopName() != null && !queryForm.getLatestShopName().trim().isEmpty()) {
                sql.append("AND 最近一次下单店铺 LIKE ? ");
                params.add("%" + queryForm.getLatestShopName() + "%");
            }

            if (queryForm.getChurnRisk() != null && !queryForm.getChurnRisk().trim().isEmpty()) {
                if ("NULL".equals(queryForm.getChurnRisk())) {
                    sql.append("AND (流失风险 IS NULL OR 流失风险 = '') ");
                } else {
                    sql.append("AND 流失风险 = ? ");
                    params.add(queryForm.getChurnRisk());
                }
            }

            if (queryForm.getMemberLevel() != null && !queryForm.getMemberLevel().trim().isEmpty()) {
                if ("NULL".equals(queryForm.getMemberLevel())) {
                    sql.append("AND (会员等级 IS NULL OR 会员等级 = '') ");
                } else {
                    sql.append("AND 会员等级 = ? ");
                    params.add(queryForm.getMemberLevel());
                }
            }

            if (queryForm.getSalesAmountMin() != null) {
                sql.append("AND 总销售额 >= ? ");
                params.add(queryForm.getSalesAmountMin());
            }

            if (queryForm.getSalesAmountMax() != null) {
                sql.append("AND 总销售额 <= ? ");
                params.add(queryForm.getSalesAmountMax());
            }

            if (queryForm.getPurchaseCountMin() != null) {
                sql.append("AND 购买次数 >= ? ");
                params.add(queryForm.getPurchaseCountMin());
            }

            if (queryForm.getPurchaseCountMax() != null) {
                sql.append("AND 购买次数 <= ? ");
                params.add(queryForm.getPurchaseCountMax());
            }

            if (queryForm.getFirstPurchaseDateBegin() != null) {
                sql.append("AND 首次成交日期 >= ? ");
                params.add(queryForm.getFirstPurchaseDateBegin());
            }

            if (queryForm.getFirstPurchaseDateEnd() != null) {
                sql.append("AND 首次成交日期 <= ? ");
                params.add(queryForm.getFirstPurchaseDateEnd());
            }

            if (queryForm.getLastPurchaseDateBegin() != null) {
                sql.append("AND 上次购买日期 >= ? ");
                params.add(queryForm.getLastPurchaseDateBegin());
            }
            
            if (queryForm.getLastPurchaseDateEnd() != null) {
                sql.append("AND 上次购买日期 <= ? ");
                params.add(queryForm.getLastPurchaseDateEnd());
            }

            sql.append("ORDER BY 上次购买日期 DESC ");
            sql.append("LIMIT ?");
            params.add(queryForm.getPageSize());

            try (PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
                for (int i = 0; i < params.size(); i++) {
                    stmt.setObject(i + 1, params.get(i));
                }

                ResultSet rs = stmt.executeQuery();
                while (rs.next()) {
                    CustomerViewVO customer = new CustomerViewVO();
                    customer.set客户唯一编码(rs.getString("客户唯一编码"));
                    customer.set总销售额(rs.getBigDecimal("总销售额"));
                    customer.set总成本(rs.getBigDecimal("总成本"));
                    customer.set总利润(rs.getBigDecimal("总利润"));
                    customer.set总客户毛利率(rs.getString("总客户毛利率"));
                    customer.set购买次数(rs.getInt("购买次数"));
                    customer.set复购周期(rs.getInt("复购周期"));
                    
                    Date firstPurchaseDate = rs.getDate("首次成交日期");
                    if (firstPurchaseDate != null) {
                        customer.set首次成交日期(firstPurchaseDate.toLocalDate());
                    }
                    
                    Date lastPurchaseDate = rs.getDate("上次购买日期");
                    if (lastPurchaseDate != null) {
                        customer.set上次购买日期(lastPurchaseDate.toLocalDate());
                    }
                    
                    customer.set流失风险(rs.getString("流失风险"));
                    customer.set上次购买后距今天数(rs.getInt("上次购买后距今天数"));
                    customer.set购买天数(rs.getInt("购买天数"));
                    customer.set最近一次下单店铺(rs.getString("最近一次下单店铺"));
                    customer.set最近下单原始单号(rs.getString("最近下单原始单号"));
                    customer.set会员等级(rs.getString("会员等级"));
                    
                    list.add(customer);
                }
            }

        } catch (SQLException e) {
            log.error("导出客户视角数据失败", e);
            throw new RuntimeException("导出客户视角数据失败: " + e.getMessage());
        }

        return list;
    }

    /**
     * 获取客户统计信息
     */
    public ResponseDTO<Object> getCustomerViewStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password)) {
            
            // 总客户数
            String totalSql = "SELECT COUNT(*) as totalCount FROM crm_客户查询";
            try (PreparedStatement stmt = connection.prepareStatement(totalSql)) {
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    statistics.put("totalCount", rs.getLong("totalCount"));
                }
            }

            // 正常客户数
            String normalSql = "SELECT COUNT(*) as normalCount FROM crm_客户查询 WHERE 流失风险 = '正常'";
            try (PreparedStatement stmt = connection.prepareStatement(normalSql)) {
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    statistics.put("normalCount", rs.getLong("normalCount"));
                }
            }

            // 已流失客户数
            String churnedSql = "SELECT COUNT(*) as churnedCount FROM crm_客户查询 WHERE 流失风险 = '已流失'";
            try (PreparedStatement stmt = connection.prepareStatement(churnedSql)) {
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    statistics.put("churnedCount", rs.getLong("churnedCount"));
                }
            }

            // 总销售额
            String salesSql = "SELECT SUM(总销售额) as totalSales FROM crm_客户查询";
            try (PreparedStatement stmt = connection.prepareStatement(salesSql)) {
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    statistics.put("totalSales", rs.getBigDecimal("totalSales"));
                }
            }

            // 总成本
            String costSql = "SELECT SUM(总成本) as totalCost FROM crm_客户查询";
            try (PreparedStatement stmt = connection.prepareStatement(costSql)) {
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    statistics.put("totalCost", rs.getBigDecimal("totalCost"));
                }
            }

            // 总利润
            String profitSql = "SELECT SUM(总利润) as totalProfit FROM crm_客户查询";
            try (PreparedStatement stmt = connection.prepareStatement(profitSql)) {
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    statistics.put("totalProfit", rs.getBigDecimal("totalProfit"));
                }
            }

        } catch (SQLException e) {
            log.error("获取客户统计信息失败", e);
            throw new RuntimeException("获取客户统计信息失败: " + e.getMessage());
        }

        return ResponseDTO.ok(statistics);
    }
} 