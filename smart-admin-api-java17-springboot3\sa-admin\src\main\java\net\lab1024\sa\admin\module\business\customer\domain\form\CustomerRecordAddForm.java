package net.lab1024.sa.admin.module.business.customer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 客户跟进记录添加表单
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:00:00
 * @Copyright 1.0
 */
@Data
public class CustomerRecordAddForm {

    @Schema(description = "客户唯一编码")
    @NotBlank(message = "客户唯一编码不能为空")
    @Length(max = 255, message = "客户唯一编码最多255字符")
    private String customerUniqueCode;

    @Schema(description = "跟进类型：phone-电话沟通,email-邮件联系,visit-上门拜访,wechat-微信沟通,problem-问题处理,other-其他")
    @NotBlank(message = "跟进类型不能为空")
    private String type;

    @Schema(description = "跟进状态：pending-待跟进,processing-跟进中,completed-已完成,cancelled-已取消")
    @NotBlank(message = "跟进状态不能为空")
    private String status;

    @Schema(description = "跟进时间")
    @NotNull(message = "跟进时间不能为空")
    private LocalDateTime followTime;

    @Schema(description = "下次跟进时间")
    private LocalDateTime nextFollowTime;

    @Schema(description = "跟进内容")
    @NotBlank(message = "跟进内容不能为空")
    @Length(max = 2000, message = "跟进内容最多2000字符")
    private String content;

    @Schema(description = "备注")
    @Length(max = 500, message = "备注最多500字符")
    private String remark;
} 