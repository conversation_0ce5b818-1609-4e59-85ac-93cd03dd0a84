* **MySQL 用户创建**：强调主机名与后端 JDBC URL 的匹配。
* **后端配置文件 (`application.yml`)**：明确指出数据库 URL 中主机名的正确写法 (`localhost` 或 `127.0.0.1`) 和 `allowPublicKeyRetrieval=true` 参数的重要性，以及 `server.port` 的正确设置。
* **后端进程管理**：加入如何查找和停止旧 Java 进程的命令。
* **前端环境变量 (`.env.production`)**：强调 `VITE_APP_API_URL` 应配置为相对路径 `'/api'` 以配合 Nginx。
* **Nginx 配置**：确保包含最终修正后的、能够正确去除 `/api` 前缀并代理到后端的 `location /api/` 配置，并指出要避免配置文件开头的多余字符。
* **SSH 端口转发**：加入如何检查本地端口占用和成功建立转发的步骤。
* **Redis (通过宝塔安装)**：加入通过宝塔面板安装和配置 Redis 的经验。

以下是根据您的要求更新后的完整部署笔记：

---
# SmartAdmin 项目部署指南 (v3 - 集成排错与成功经验)

本文档将指导您如何在 Ubuntu 服务器上部署 SmartAdmin 项目的前后端应用，并集成了部署过程中遇到的问题和已验证的解决方案。

## 一、服务器准备

1.  **购买并配置云服务器**：
    * 推荐配置：2核4G内存，5M带宽，50G系统盘。
    * 操作系统：Ubuntu 20.04 LTS 或更高版本。
    * 安全组配置：确保开放以下端口：
        * `22` (SSH - 用于远程登录和端口转发)
        * `80` (HTTP - 用于 Nginx)
        * `443` (HTTPS - 用于 Nginx SSL)
        * `3306` (MySQL数据库默认端口，如果在本机安装)
        * `6379` (Redis默认端口，如果通过宝塔安装并使用默认设置)
        * 后端Java应用端口 (例如 `8080`，确保与Nginx配置一致，此端口不需要对外网开放，由Nginx代理)

2.  **安装必要软件**：
    * 通过 SSH 连接到您的服务器。
    * 更新软件包列表：
        ```bash
        sudo apt update
        sudo apt upgrade -y
        ```
    * **(可选) 配置国内 APT 镜像源** (例如阿里云，如果默认源下载速度慢或无法访问):
        * **重要提示：** 在修改前，请务必备份原始的源配置文件。
            * Ubuntu 20.04 及更早版本: `sudo cp /etc/apt/sources.list /etc/apt/sources.list.bak`
            * Ubuntu 22.04 及更高版本 (如果存在 `.sources` 文件): `sudo cp /etc/apt/sources.list.d/ubuntu.sources /etc/apt/sources.list.d/ubuntu.sources.bak`
        * 根据您的 Ubuntu 版本和阿里云官方镜像站的指引修改源文件。
        * 修改后清理并更新：
            ```bash
            sudo apt clean
            sudo apt update
            sudo apt upgrade -y
            ```
    * **安装 Java (OpenJDK 17 或更高版本，根据您的后端项目选择):**
        ```bash
        sudo apt install openjdk-17-jdk -y
        java -version
        ```
    * **(可选) 安装 Maven (如果需要在服务器上构建后端项目):**
        ```bash
        sudo apt install maven -y
        mvn -version
        ```
    * **安装 Nginx:**
        ```bash
        sudo apt install nginx -y
        sudo systemctl start nginx
        sudo systemctl enable nginx
        ```
    * **安装 Node.js 和 npm (推荐使用 nvm 进行版本管理):**
        ```bash
        curl -o- https://gitee.com/mirrors/nvm/raw/master/install.sh | bash
        # 使 nvm 命令生效 (可能需要重新登录或执行以下命令使当前会话生效)
        export NVM_DIR="$HOME/.nvm"
        [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
        [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
        # 安装 Node.js (例如 v18)
        nvm install 18
        nvm use 18 # 设为当前使用版本
        node -v
        npm -v
        ```
    * **安装 Git:**
        ```bash
        sudo apt install git -y
        ```

---
## 二、数据库配置 (MySQL)

1.  **安装 MySQL 数据库** (如果选择在服务器上自行安装):
    ```bash
    sudo apt install mysql-server -y
    sudo mysql_secure_installation # 运行安全配置向导，设置root密码等
    sudo systemctl start mysql
    sudo systemctl enable mysql
    ```
    或者使用云服务商提供的 RDS 数据库服务。

2.  **创建数据库和用户**：
    * 登录 MySQL (通常以 root 用户身份):
        ```bash
        sudo mysql -u root -p
        ```
    * 创建数据库 (例如 `smart_admin_v3`，与后端配置文件中的名称一致):
        ```sql
        CREATE DATABASE smart_admin_v3 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        ```
    * 创建后端应用连接数据库所用的用户，并授予权限。**关键：确保主机名 (`host`) 与后端应用配置文件 (`application.yml`) 中 JDBC URL 指定的主机名匹配。**
        * 如果后端应用配置文件中 JDBC URL 使用 `localhost` 或 `127.0.0.1` (推荐方式，当数据库和应用在同一服务器时)：
            ```sql
            CREATE USER 'sa'@'localhost' IDENTIFIED BY 'wangbo1234..'; -- 请务必替换为安全的强密码!
            GRANT ALL PRIVILEGES ON smart_admin_v3.* TO 'sa'@'localhost';
            ```
        * **(重要经验)** 如果您需要从 Navicat 等外部工具（例如从 IP `************`）连接管理数据库，您需要为该特定 IP 或更通用的主机模式（如 `'192.168.0.%'` 或 `'%'`，但请注意 `'%'` 的安全风险）创建一个**单独的**用户记录或授权。例如，为 Navicat 客户端 IP 创建用户：
            ```sql
            -- 示例：为 Navicat 客户端 IP ************ 创建用户，密码应独立设置且安全
            -- CREATE USER 'sa_remote'@'************' IDENTIFIED BY 'another_strong_password';
            -- GRANT ALL PRIVILEGES ON smart_admin_v3.* TO 'sa_remote'@'************';
            -- 或者，如果您确认 'sa'@'localhost' 的密码也用于远程，且希望用同一个用户名（不推荐）
            -- CREATE USER 'sa'@'************' IDENTIFIED BY 'wangbo1234..'; -- 使用与 sa@localhost 相同的密码
            -- GRANT ALL PRIVILEGES ON smart_admin_v3.* TO 'sa'@'************';
            ```
            **重点：**MySQL 中 `'user'@'host'` 组合是唯一的。`'sa'@'localhost'` 和 `'sa'@'************'` 是两个不同的用户。`GRANT` 命令在 MySQL 8.0+ 中不能自动创建用户，必须先 `CREATE USER`。
    * 刷新权限：
        ```sql
        FLUSH PRIVILEGES;
        EXIT;
        ```

3.  **导入数据库脚本**：
    * 将项目提供的 SQL 脚本 (例如 `smart_admin_v3.sql`) 上传到服务器。
    * 导入数据 (使用上一步创建的用于应用连接的用户名 `sa` 和数据库名 `smart_admin_v3`)：
        ```bash
        mysql -u sa -p smart_admin_v3 < /path/to/your/smart_admin_v3.sql
        ```
        (提示时输入 `sa` 用户的密码 `wangbo1234..`)
    * 如果项目有后续的数据库更新脚本 (例如在 `sql/sql-update-log/` 目录)，请按照版本顺序执行。

---
## 三、Redis 配置 (通过宝塔面板安装)

使用宝塔Linux面板安装和管理 Redis 可以简化很多操作，包括权限和自启动设置。

1.  **安装宝塔面板：**
    * 参照宝塔官方网站 ([https://www.bt.cn/](https://www.bt.cn/)) 的指引，在您的服务器上安装宝塔面板。
    * 安装完成后，通过浏览器访问面板地址，并完成初始化设置。

2.  **通过宝塔面板安装 Redis：**
    * 登录宝塔面板。
    * 进入“软件商店” -> “运行环境”或“应用分类”找到 Redis。
    * 选择您需要的版本（例如 Redis 7.0.x 或与项目兼容的版本）进行“一键安装”。
    * **宝塔面板会自动处理**：Redis 的下载、安装、创建 `redis` 用户/组、配置基础 `redis.conf`、**设置必要的文件和目录权限**、以及将 Redis 添加到系统服务并设置开机自启。
    * **检查 Redis 状态与配置：** 安装完成后，在宝塔面板的 Redis 管理界面（通常在软件列表点击Redis的“设置”），您可以查看 Redis 的运行状态（应为“运行中”）、监听端口（默认为 `6379`）、配置文件路径，并可以设置密码（`requirepass`）。**经验表明，通过宝塔安装后，服务通常能直接正常运行。**

3.  **后端应用连接 Redis 的配置：**
    * 在 SmartAdmin 后端应用的配置文件 (`application.yml` 或 `application-prod.yml`) 中，找到 `spring.data.redis` 部分。
    * 根据宝塔面板中 Redis 的实际情况配置：
        ```yaml
        spring:
          data:
            redis:
              database: 1  # 根据需要选择 Redis 数据库编号
              host: 127.0.0.1 # 如果 Redis 和后端应用在同一台服务器，使用 127.0.0.1
              port: 6379     # 宝塔安装的 Redis 默认端口
              password:      # 如果您在宝塔中为 Redis 设置了密码，请填写在这里，否则留空
              timeout: 10000ms
              lettuce:
                pool:
                  max-active: 100
                  min-idle: 10
                  max-idle: 50
                  max-wait: 30000ms
        ```

---
## 四、后端部署 (Java Spring Boot)

1.  **获取后端代码**：
    * 将本地开发环境中打包好的 `.jar` 文件 (例如 `sa-admin-prod-3.0.0.jar`，**注意文件名，之前有过 `...back.jar` 的笔误导致无法访问 JAR 文件**) 上传到服务器的目标部署目录 (例如 `/opt/smartadmin/backend/`)。
    mvn clean package -Pprod -DskipTests 生产环境打包jar包命令

2.  **修改配置文件**：
    * 后端应用的配置文件通常与 `.jar` 文件放在同一目录或其下的 `config` 子目录，并命名为 `application.yml` 或 `application-prod.yml` (取决于您启动时通过 `-Dspring.profiles.active=prod` 指定的profile)。
    * **关键配置项检查与修正**：
        * **数据库连接信息 (`spring.datasource.url`)**:
            * **确保主机名正确**：应为 `******************************************?...` 或 `******************************************?...` (之前遇到的 `loaclhost` 拼写错误已修正)。
            * **确保包含 `&allowPublicKeyRetrieval=true`** 参数，以避免 "Public Key Retrieval is not allowed" 错误。
            * 完整的正确示例：
            ```yaml
            # spring.datasource 下
            url: **********************************************************************************************************************************************************************************************************************************************
            username: sa # (MySQL中'sa'@'localhost'的用户名)
            password: wangbo1234.. # (MySQL中'sa'@'localhost'的密码)
            ```
        * **服务器端口 (`server.port`)**: 确保设置为 Nginx 反向代理期望的端口，例如 `8080`。**如果之前遇到端口冲突（如1024端口被占用），务必在此明确指定一个可用且与Nginx配置一致的端口。**
            ```yaml
            server:
              port: 8080
            ```
        * **Redis 连接信息 (`spring.data.redis`)**: 根据上一节宝塔安装的 Redis 情况配置。
    * **YAML 语法检查**：确保配置文件遵循正确的 YAML 缩进规则，避免因缩进错误导致解析失败（如之前遇到的 `expected <block end>, but found '<block mapping start>'` 错误，通常是由于缺少键或缩进不一致）。

3.  **运行后端应用**：
    * **停止可能存在的旧的或失败的 Java 进程**：
        ```bash
        # 查找 Java 进程 (将 'sa-admin-prod-3.0.0.jar' 替换为您的实际 JAR 文件名)
        ps aux | grep 'sa-admin-prod-3.0.0.jar'
        # 在输出中找到对应的进程ID (PID)，例如 68673, 77653
        # 停止这些进程 (如果有多个，都停止)
        sudo kill <PID1> # 例如: sudo kill 68673
        sudo kill <PID2> # 例如: sudo kill 77653
        # 确认进程已停止
        # ps aux | grep 'sa-admin-prod-3.0.0.jar' (应该只剩下 grep 命令本身或报告后台任务已退出)
        ```
    * **启动新应用**：
        ```bash
        cd /opt/smartadmin/backend/ # 确保在此目录下
        nohup java -jar -Dspring.profiles.active=prod sa-admin-prod-3.0.0.jar > app.log 2>&1 &
        ```

4.  **验证后端服务**：
    * 实时查看应用启动日志：
        ```bash
        tail -f /opt/smartadmin/backend/app.log
        ```
    * 确认没有数据库连接错误、端口占用错误，并看到类似 "【sa-admin】 服务已成功启动" 以及监听在正确端口（例如 `服务本机地址: http://localhost:8080/`）的信息。
    * 日志中会显示 API 文档地址（如 Swagger 或 knife4j），例如 `http://localhost:8080/doc.html`。

---
## 五、前端部署 (Vue)

1.  **修改前端 API 地址配置 (`.env.production`)** (在前端项目源码的开发环境中修改，然后**必须重新构建**)：
    * 打开前端项目的 `.env.production` 文件。
    * **将 `VITE_APP_API_URL` 修改为指向 Nginx 代理的相对路径 `/api`**，这是解决之前前端网络错误的关键：
        ```env
        NODE_ENV=production
        VITE_APP_TITLE='SmartAdmin V3.X'
        VITE_APP_API_URL='/api'
        ```

2.  **重新构建前端应用** (在您的开发或构建环境中执行)：
    * 进入前端项目根目录。
    * 如果是首次构建或依赖有变动，先执行：`npm install` (或 `pnpm install` / `yarn install`)
    * 构建生产环境包：`npm run build:prod` (具体命令看项目的 `package.json` 文件)
    * 构建成功后，会在项目根目录下生成 `dist` 文件夹。

3.  **部署前端文件到服务器**：
    * 将本地新构建的 `dist` 文件夹内的所有内容上传并覆盖到 Nginx 的 Web 根目录 (例如 `/var/www/smartadmin/frontend/`)。
        ```bash
        sudo mkdir -p /var/www/smartadmin/frontend
        # 示例：使用 rsync 从本地同步 (在本地机器执行，确保替换your_username和your_server_ip)
        # rsync -avz --delete /path/to/your/local/frontend/dist/* your_username@your_server_ip:/var/www/smartadmin/frontend/
        # 或者先上传压缩包到服务器再解压覆盖
        ```

4.  **配置 Nginx**：
    * 创建或修改 Nginx 站点配置文件 (例如 `/etc/nginx/sites-enabled/smartadmin.conf`)。**确保配置文件开头没有多余的 "nginx" 字符**（之前已修正），并且 `location /api/` 的配置能正确去除 `/api` 前缀并代理到后端，同时包含处理上传文件路径的 `location /uploads/` 配置：
        ```nginx
        server {
            listen 80;
            server_name *************; # 替换为您的实际域名或服务器公网IP

            root /var/www/smartadmin/frontend; # 前端文件根目录
            index index.html index.htm;

            location / {
                try_files $uri $uri/ /index.html;
            }

            # 后端 API 反向代理配置
            location /api/ {
                rewrite ^/api/(.*)$ /$1 break;  # 去掉 /api/ 前缀
                proxy_pass http://localhost:8080; # 转发到后端的根路径 (后端监听8080)
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # 文件上传访问路径 (与后端 url-prefix 匹配)
            location /uploads/ {
                alias /home/<USER>/upload/; # 指向后端 upload-path 配置的物理路径
                autoindex off; # 可选，禁止列出目录内容
                expires 30d;   # 可选，设置浏览器缓存时间
            }

            error_log /var/log/nginx/smartadmin_error.log;
            access_log /var/log/nginx/smartadmin_access.log;
        }
        ```
    * **测试 Nginx 配置**：
        ```bash
        sudo nginx -t
        ```
        确保输出 `syntax is ok` 和 `test is successful`。
    * **重新加载 Nginx 配置**：
        ```bash
        sudo systemctl reload nginx
        ```
        检查状态：`sudo systemctl status nginx`，确保是 `active (running)` 且最近的 reload 操作成功。

    * **(重要经验) 处理文件上传与访问 (例如头像加载)**：
        * **问题现象**：前端上传的图片（如用户头像）无法正常显示，浏览器控制台可能报告图片 URL 访问 404 错误，或者图片 URL 指向了后端服务的内部 IP 和端口 (例如 `http://**********:8080/upload/...`)，而不是通过 Nginx 代理的地址。
        * **原因分析**：
            1.  **后端生成的文件 URL 不正确**：后端服务在生成文件访问 URL 时，可能未使用外部可访问的域名或 IP，或者未使用 Nginx 代理的路径前缀。例如，`FileStorageLocalServiceImpl.java` 中 `urlPrefix` 如果未通过配置文件 (`sa-base.yaml` 中的 `file.storage.local.url-prefix`) 正确配置，可能会默认使用服务器的内部 IP 和端口。
            2.  **Nginx 未正确配置静态资源代理**：Nginx 需要配置一个 `location`块来处理文件上传路径的请求，并将其指向实际的文件存储目录。
            3.  **前端 API URL 与文件 URL 不一致**：前端 `.env.production` 中的 `VITE_APP_API_URL` (例如 `/api`) 用于 API 请求，但后端生成的文件 URL 可能未使用此统一前缀，导致访问方式不一致。
        * **解决方案**：
            1.  **统一后端文件 URL 生成逻辑**：
                *   修改后端 `sa-base` 模块的生产环境配置文件 (例如 `sa-base/src/main/resources/prod/sa-base.yaml`)，确保 `file.storage.local.url-prefix` 配置为通过 Nginx 访问的路径前缀。例如，如果 Nginx 配置了 `/uploads/` 路径来提供静态文件，则这里应配置为：
                    ```yaml
                    file:
                      storage:
                        local:
                          # 本地存储路径 (例如 /home/<USER>/upload/)
                          upload-path: /home/<USER>/upload/
                          # 访问URL前缀 (关键！确保与Nginx配置匹配)
                          url-prefix: /uploads/
                    ```
                *   这样，后端生成的图片 URL 会是类似 `/uploads/public/common/your_image.jpg` 的相对路径。
            2.  **配置 Nginx 代理静态文件**：
                *   在 Nginx 的站点配置文件 (例如 `/etc/nginx/sites-enabled/smartadmin.conf`) 中，添加一个新的 `location` 块来处理文件上传路径的请求。这个 `location` 应该与后端配置文件中的 `url-prefix` 对应，并使用 `alias` 指令指向实际的文件存储目录 (`upload-path`)。
                    ```nginx
                    server {
                        # ... 其他配置 ...

                        # API 代理
                        location /api/ {
                            rewrite ^/api/(.*)$ /$1 break;
                            proxy_pass http://localhost:8080;
                            # ... 其他 proxy_set_header ...
                        }

                        # 文件上传访问路径 (与后端 url-prefix 匹配)
                        location /uploads/ {
                            alias /home/<USER>/upload/; # 指向后端 upload-path 配置的物理路径
                            autoindex off; # 可选，禁止列出目录内容
                            expires 30d;   # 可选，设置浏览器缓存时间
                        }

                        # ... 其他配置 ...
                    }
                    ```
                *   **注意**：`alias` 指令后的路径必须以 `/` 结尾，如果 `location` 路径也以 `/` 结尾。`alias` 会将 `location` 匹配的路径替换为 `alias` 指定的路径。例如，请求 `/uploads/some/file.jpg` 会被映射到服务器上的 `/home/<USER>/upload/some/file.jpg`。
            3.  **重启服务与验证**：
                *   修改后端配置文件后，**重启后端 Java 应用**。
                *   修改 Nginx 配置文件后，**测试配置 (`sudo nginx -t`) 并重新加载 Nginx (`sudo systemctl reload nginx`)**。
                *   **清除浏览器缓存并强制刷新前端页面**，检查头像等图片是否能正常加载。

5.  **验证前端应用**：
    * 在浏览器中访问您的服务器 IP 地址 (例如 `http://*************`)。
    * **务必清除浏览器缓存并强制刷新** (通常是 `Ctrl+Shift+R` 或 `Cmd+Shift+R`)。
    * 检查前端页面是否正常加载，登录、获取验证码等功能是否正常。同时查看浏览器开发者工具的网络(Network)和控制台(Console)标签页，确认 API 请求路径正确 (应该是 `http://*************/api/...`) 且没有404、CORS或其它网络错误。

---
## 六、通过 SSH 端口转发访问服务器本地服务 (例如 API 文档)

如果您需要在本地电脑上通过浏览器访问运行在服务器 `localhost` 上的服务（例如后端应用在 `localhost:8080` 上提供的 API 文档），可以使用 SSH 本地端口转发。

1.  **确认本地端口未被占用：**
    在您的**本地电脑**上，打开终端或 PowerShell，检查您想使用的本地端口（例如 `8889`，如果 `8888` 被占用）是否空闲。
    * **Linux/macOS:** `sudo ss -tulnp | grep ':8889'`
    * **Windows (PowerShell):** `netstat -ano | findstr ":8889"`
    如果没有输出，表示端口可用。如果被占用，请先停止占用该端口的程序（可能是之前未关闭的 SSH 隧道或其他应用），或选择另一个本地端口。

2.  **建立 SSH 端口转发连接：**
    在您的**本地电脑**上执行 (将 `test` 替换为您的服务器用户名，`*************` 替换为您的服务器IP，`8889` 替换为您选定的本地空闲端口，`8080` 替换为服务器上后端应用实际监听的端口)：
    ```bash
    ssh -L 8889:localhost:8080 test@*************
    ```
    * 输入服务器用户密码。
    * **保持这个 SSH 连接窗口打开且连接成功（没有报端口转发失败的错误）。**

3.  **在本地浏览器中访问：**
    打开浏览器，访问 `http://localhost:本地端口/目标路径`。
    例如，如果后端 API 文档在服务器上的路径是 `http://localhost:8080/doc.html`，那么您在本地浏览器中访问：
    `http://localhost:8889/doc.html` （如果本地使用了8889端口）

---
## 七、常见问题与维护

1.  **日志查看**：
    * 后端应用日志：`/opt/smartadmin/backend/app.log`。
    * Nginx 日志：`/var/log/nginx/smartadmin_error.log` 和 `/var/log/nginx/smartadmin_access.log`。
    * Redis 日志：通过宝塔面板查看，或在其配置文件中找到日志路径。

2.  **应用更新**：
    * **后端**：停止旧的应用进程，上传新的 `.jar` 文件，然后重新用 `nohup` 命令运行。
    * **前端**：在开发环境重新构建前端项目，然后将新的 `dist` 目录内容覆盖到 Nginx 的 Web 根目录。

3.  **防火墙**：
    * **服务器安全组**：确保已开放 80, 443, 22。3306 和 6379 通常不需要对外开放，除非有特定需求。后端应用端口（如8080）由Nginx代理，也不需要对外开放。
    * **宝塔面板防火墙**：如果使用，确保放行 80, 443。
    * **服务器系统防火墙 (ufw)**：如果启用了`ufw`：
        ```bash
        sudo ufw allow 'Nginx Full' # 允许 HTTP (80) 和 HTTPS (443)
        sudo ufw allow 22/tcp      # SSH
        sudo ufw enable
        sudo ufw status
        ```

4.  **HTTPS 配置**：
    * 推荐使用 Let's Encrypt。宝塔面板通常集成了此功能。
    * 手动配置可使用 Certbot：
        ```bash
        sudo apt install certbot python3-certbot-nginx -y
        sudo certbot --nginx -d your_domain_or_ip # 替换为您的域名或IP
        ```

---
## 八、移动端部署 (uniapp)

(如果需要，请参考您之前的笔记或项目官方文档)

---
好的，这里是只包含成功安装和配置步骤的精简版经验总结，可以作为一个更直接的指南：

---

# 快速指南：在 Ubuntu 上成功安装 Docker 及 Dockerized Redis

本文档总结了在 Ubuntu 系统上成功安装 Docker Engine 并部署具有自动启动和数据持久化功能的 Redis 容器的关键步骤。

## 阶段一：安装和配置 Docker Engine

**1. 系统准备与清理 (可选但推荐)**
* 确保系统已更新：`sudo apt-get update && sudo apt-get upgrade -y`
* 如果之前有 Docker 安装残留且导致问题，建议先彻底卸载（参考详细排错文档中的卸载步骤）。关键是移除相关软件包、配置文件、数据目录以及手动放置的可执行文件。

**2. 安装 Docker Engine (官方推荐方法)**

   **2.1 更新软件包列表**
   ```bash
   sudo apt-get update
   ```

   **2.2 安装必要的依赖包**
   允许 `apt` 通过 HTTPS 使用仓库：
   ```bash
   sudo apt-get install apt-transport-https ca-certificates curl software-properties-common gnupg lsb-release -y
   ```

   **2.3 添加 Docker 的官方 GPG 密钥**
   如果直接从 `download.docker.com` 下载 GPG 密钥遇到网络问题，可以尝试使用可靠的镜像源（如阿里云）获取：
   ```bash
   # 创建密钥环目录
   sudo install -m 0755 -d /etc/apt/keyrings
   # 从阿里云镜像下载 GPG 密钥 (如果官方源不稳定)
   curl -fsSL https://mirrors.aliyun.com/docker-ce/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
   # 设置权限
   sudo chmod a+r /etc/apt/keyrings/docker.gpg
   ```

   **2.4 设置 Docker 的 APT 仓库源**
   创建或编辑 `/etc/apt/sources.list.d/docker.list` 文件，使用 Docker 官方仓库地址，并确保架构和发行代号正确（例如 `amd64` 和 `noble`）：
   ```bash
   sudo nano /etc/apt/sources.list.d/docker.list
   ```
   文件内容应为 (将 `amd64` 和 `noble` 替换为你的实际架构和发行代号)：
   ```
   deb [arch=amd64 signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu noble stable
   ```

   **2.5 检查并移除错误的 APT 代理配置 (关键排错点)**
   如果 `sudo apt-get update` 在添加外部仓库后失败并提示代理错误或连接到非预期服务器（如 Tengine），检查并禁用系统中可能存在的错误 APT 代理配置。例如，我们之前发现并处理了 `/etc/apt/apt.conf.d/90curtin-aptproxy` 文件中的错误配置：
   ```bash
   # 查找可能的代理配置
   # grep -E -i -r 'proxy|acquire::http::proxy|acquire::https::proxy' /etc/apt/apt.conf* /etc/environment
   # 如果找到类似 90curtin-aptproxy 这样的错误配置，将其备份并移除/禁用
   sudo mv /etc/apt/apt.conf.d/90curtin-aptproxy /etc/apt/apt.conf.d/90curtin-aptproxy.bak # 示例
   ```
   修正后，再次运行 `sudo apt-get update` 确保能成功连接到 Docker 仓库。

   **2.6 安装 Docker Engine 软件包**
   ```bash
   sudo apt-get update # 确保在修复代理或源问题后再次更新
   sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin -y
   ```

   **2.7 配置 Docker Hub 镜像加速器 (关键步骤)**
   为避免从 Docker Hub 拉取镜像时遇到网络问题 (如 `connection reset by peer`)，配置一个可靠的镜像加速器。
   编辑或创建 `/etc/docker/daemon.json` 文件：
   ```bash
   sudo nano /etc/docker/daemon.json
   ```
   添加你的加速器地址 (以下为示例，请替换为你的有效加速器地址，如阿里云专属加速器或 DaoCloud 等)：
   ```json
   {
     "registry-mirrors": ["https://<你的有效加速器地址>"]
   }
   ```
   例如，使用 DaoCloud：
   ```json
   {
     "registry-mirrors": ["https://docker.m.daocloud.io"]
   }
   ```
   然后重载配置并重启 Docker：
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl restart docker
   ```

   **2.8 验证 Docker 安装**
   运行 `hello-world` 容器测试：
   ```bash
   sudo docker run hello-world
   ```
   *预期成功输出 "Hello from Docker!"*

**3. (可选) 配置 Docker 非 root 用户访问**
   ```bash
   sudo groupadd docker  # 如果组不存在
   sudo usermod -aG docker $USER
   # 注销并重新登录以使用户组更改生效
   ```

---
## 阶段二：使用 Docker 安装和运行 Redis

**1. 确保端口可用**
   在运行 Redis 容器前，确保宿主机的 `6379` 端口未被其他服务占用。如果被占用 (例如被系统自带的 Redis 服务)，请先停止并禁用冲突的服务。
   ```bash
   # 检查端口占用
   # sudo ss -tulnp | grep ':6379'
   # 如果有冲突，停止并禁用相关服务 (示例针对系统 Redis)
   # sudo systemctl stop redis-server.service
   # sudo systemctl disable redis-server.service
   # sudo kill <PID> # 如果 systemctl 未能停止进程
   ```

**2. 移除先前冲突的容器 (如果存在)**
   如果因先前尝试失败而存在同名容器，请先移除：
   ```bash
   sudo docker rm my-redis # 替换为你的容器名
   ```

**3. 运行 Redis 容器 (包含自动启动和数据持久化)**
   ```bash
   sudo docker run --name my-redis -d \
     --restart unless-stopped \
     -p 127.0.0.1:6379:6379 \
     -v redis_data:/data \
     redis
   ```
   * `--name my-redis`：指定容器名称。
   * `-d`：后台运行。
   * `--restart unless-stopped`：设置容器在除非手动停止外总是自动重启。
   * `-p 127.0.0.1:6379:6379`：将容器的 6379 端口映射到宿主机的 `127.0.0.1:6379` (仅本地可访问，更安全)。如需外部访问，可改为 `-p 6379:6379` (注意防火墙)。
   * `-v redis_data:/data`：创建一个名为 `redis_data` 的 Docker 命名卷，并将其挂载到容器内的 `/data` 目录，用于持久化 Redis 数据。
   * `redis`：使用官方最新的 Redis 镜像。

---
## 阶段三：验证 Redis 服务

**1. 检查容器状态**
   ```bash
   sudo docker ps
   ```
   *应能看到名为 `my-redis` 的容器正在运行 (状态 "Up")。*

**2. 测试 Redis 连接**
   ```bash
   sudo docker exec -it my-redis redis-cli ping
   ```
   *预期输出: `PONG`*

   如果你在宿主机安装了 `redis-cli`，也可以使用：
   ```bash
   redis-cli -h 127.0.0.1 -p 6379 ping
   ```

---
## 总结

通过以上步骤，你可以成功在 Ubuntu 系统上配置好 Docker 环境，并运行一个具有自动启动和数据持久化能力的 Redis 服务。关键在于确保 Docker 本身安装正确、网络通畅 (通过配置镜像加速器和解决代理问题)，并正确处理端口和容器命名冲突。

部署过程中请根据您的实际项目结构和需求进行调整。祝您部署顺利！