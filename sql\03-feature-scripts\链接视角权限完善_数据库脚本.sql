-- =====================================================================
--   链接视角权限完善SQL脚本
--   目的：为链接视角页面添加完整的权限控制
--   作者：SmartAdmin权限优化
--   日期：2025-06-25
-- =====================================================================

-- 1. 首先查询链接视角主菜单ID
SET @link_menu_id = (SELECT menu_id FROM t_menu WHERE menu_name = '链接视角' AND parent_id = 306);

-- 2. 检查当前链接视角下的权限点
SELECT 
    menu_id, 
    menu_name, 
    api_perms, 
    web_perms, 
    sort 
FROM t_menu 
WHERE parent_id = @link_menu_id 
ORDER BY sort;

-- 3. 更新现有权限点的api_perms字段，确保与Controller注解一致
-- 3.1 更新查询权限
UPDATE t_menu 
SET api_perms = 'customer:link:query'
WHERE parent_id = @link_menu_id 
  AND web_perms = 'customer:link:query'
  AND api_perms != 'customer:link:query';

-- 3.2 更新详情权限（如果存在）
UPDATE t_menu 
SET api_perms = 'customer:link:detail'
WHERE parent_id = @link_menu_id 
  AND web_perms = 'customer:link:detail'
  AND api_perms != 'customer:link:detail';

-- 3.3 更新管理权限（如果存在）
UPDATE t_menu 
SET api_perms = 'customer:link:manage'
WHERE parent_id = @link_menu_id 
  AND web_perms = 'customer:link:manage'
  AND api_perms != 'customer:link:manage';

-- 4. 添加新的权限点
-- 4.1 重置权限
INSERT INTO t_menu (
    menu_name, menu_type, parent_id, sort, path, component,
    perms_type, api_perms, web_perms, icon, context_menu_id,
    frame_flag, frame_url, cache_flag, visible_flag, disabled_flag,
    deleted_flag, create_user_id, create_time, update_user_id, update_time
) VALUES (
    '重置查询', 3, @link_menu_id, 4, null, null,
    2, 'customer:link:reset', 'customer:link:reset', null, null,
    0, null, 0, 1, 0,
    0, 1, NOW(), null, NOW()
) ON DUPLICATE KEY UPDATE 
    api_perms = VALUES(api_perms),
    web_perms = VALUES(web_perms),
    update_time = NOW();

-- 4.2 统计分析权限
INSERT INTO t_menu (
    menu_name, menu_type, parent_id, sort, path, component,
    perms_type, api_perms, web_perms, icon, context_menu_id,
    frame_flag, frame_url, cache_flag, visible_flag, disabled_flag,
    deleted_flag, create_user_id, create_time, update_user_id, update_time
) VALUES (
    '统计分析', 3, @link_menu_id, 5, null, null,
    2, 'customer:link:statistics', 'customer:link:statistics', null, null,
    0, null, 0, 1, 0,
    0, 1, NOW(), null, NOW()
) ON DUPLICATE KEY UPDATE 
    api_perms = VALUES(api_perms),
    web_perms = VALUES(web_perms),
    update_time = NOW();

-- 4.3 下载权限
INSERT INTO t_menu (
    menu_name, menu_type, parent_id, sort, path, component,
    perms_type, api_perms, web_perms, icon, context_menu_id,
    frame_flag, frame_url, cache_flag, visible_flag, disabled_flag,
    deleted_flag, create_user_id, create_time, update_user_id, update_time
) VALUES (
    '下载明细', 3, @link_menu_id, 6, null, null,
    2, 'customer:link:download', 'customer:link:download', null, null,
    0, null, 0, 1, 0,
    0, 1, NOW(), null, NOW()
) ON DUPLICATE KEY UPDATE 
    api_perms = VALUES(api_perms),
    web_perms = VALUES(web_perms),
    update_time = NOW();

-- 5. 为管理员角色分配新权限（假设管理员角色ID为1）
-- 5.1 为管理员角色分配重置权限
INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT 1, menu_id
FROM t_menu
WHERE parent_id = @link_menu_id
  AND web_perms = 'customer:link:reset';

-- 5.2 为管理员角色分配统计分析权限
INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT 1, menu_id
FROM t_menu
WHERE parent_id = @link_menu_id
  AND web_perms = 'customer:link:statistics';

-- 5.3 为管理员角色分配下载权限
INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT 1, menu_id
FROM t_menu
WHERE parent_id = @link_menu_id
  AND web_perms = 'customer:link:download';

-- 6. 验证权限配置
SELECT 
    '=== 链接视角权限配置验证 ===' AS '验证结果';

-- 6.1 查看所有链接视角权限点
SELECT 
    menu_id, 
    menu_name, 
    menu_type,
    sort,
    api_perms, 
    web_perms,
    visible_flag,
    disabled_flag
FROM t_menu 
WHERE parent_id = @link_menu_id 
ORDER BY sort;

-- 6.2 查看管理员角色的链接视角权限分配
SELECT 
    rm.role_id,
    m.menu_id,
    m.menu_name,
    m.api_perms,
    m.web_perms
FROM t_role_menu rm
JOIN t_menu m ON rm.menu_id = m.menu_id
WHERE m.parent_id = @link_menu_id
  AND rm.role_id = 1
ORDER BY m.sort;

-- 7. 输出完成信息
SELECT 
    '链接视角权限配置已完成！' AS '操作结果',
    '请重启后端服务以清除权限缓存' AS '注意事项1',
    '用户需要重新登录以获取最新权限' AS '注意事项2';

-- 8. 权限点说明
SELECT 
    'customer:link:query' AS '权限编码',
    '查询链接分析数据' AS '权限说明',
    '基础权限，允许查询和查看链接分析数据' AS '权限描述'
UNION ALL
SELECT 
    'customer:link:reset' AS '权限编码',
    '重置查询条件' AS '权限说明',
    '操作权限，允许重置查询表单' AS '权限描述'
UNION ALL
SELECT 
    'customer:link:statistics' AS '权限编码',
    '查看统计分析' AS '权限说明',
    '分析权限，允许查看统计面板和图表' AS '权限描述'
UNION ALL
SELECT 
    'customer:link:download' AS '权限编码',
    '下载明细数据' AS '权限说明',
    '下载权限，允许导出Excel明细数据' AS '权限描述';
