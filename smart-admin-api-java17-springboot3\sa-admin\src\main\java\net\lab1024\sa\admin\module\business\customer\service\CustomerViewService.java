package net.lab1024.sa.admin.module.business.customer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.customer.dao.CustomerViewDao;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerViewQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerViewVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.util.SmartExcelUtil;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 客户视角 Service
 *
 * <AUTHOR>
 * @Date 2025-01-09 15:00:00
 * @Copyright 1.0
 */

@Service
public class CustomerViewService {

    @Resource
    private CustomerViewDao customerViewDao;

    @Resource
    private LirunJdbcService lirunJdbcService;

    /**
     * 分页查询客户视角 - 从lirun数据库查询
     */
    public ResponseDTO<PageResult<CustomerViewVO>> queryPage(CustomerViewQueryForm queryForm) {
        return lirunJdbcService.queryCustomerViewPage(queryForm);
    }

    /**
     * 查询客户详情 - 从lirun数据库查询
     */
    public ResponseDTO<CustomerViewVO> getDetail(String customerUniqueCode) {
        return lirunJdbcService.getCustomerViewDetail(customerUniqueCode);
    }

    /**
     * 导出客户数据 - 从lirun数据库查询
     */
    public void exportExcel(CustomerViewQueryForm queryForm) {
        try {
            queryForm.setPageSize(5000L); // 限制导出数量
            List<CustomerViewVO> dataList = lirunJdbcService.exportCustomerViewQuery(queryForm);

            // 获取HttpServletResponse
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletResponse response = attributes.getResponse();
                SmartExcelUtil.exportExcel(response, "客户视角数据", "客户视角", CustomerViewVO.class, dataList);
            }
        } catch (IOException e) {
            throw new RuntimeException("导出客户视角数据失败", e);
        }
    }

    /**
     * 获取客户统计信息 - 从lirun数据库查询
     */
    public ResponseDTO<Object> getStatistics() {
        return lirunJdbcService.getCustomerViewStatistics();
    }

    // ==================== 原有方法（从默认数据库查询，保留作为备用） ====================

    /**
     * 分页查询客户视角 - 从默认数据库查询（备用方法）
     */
    public ResponseDTO<PageResult<CustomerViewVO>> queryPageFromDefault(CustomerViewQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<CustomerViewVO> list = customerViewDao.queryPage(page, queryForm);
        PageResult<CustomerViewVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 查询客户详情 - 从默认数据库查询（备用方法）
     */
    public ResponseDTO<CustomerViewVO> getDetailFromDefault(String customerUniqueCode) {
        CustomerViewVO customerVO = customerViewDao.getDetail(customerUniqueCode);
        if (customerVO == null) {
            return ResponseDTO.userErrorParam("客户不存在");
        }
        return ResponseDTO.ok(customerVO);
    }

    /**
     * 获取客户统计信息 - 从默认数据库查询（备用方法）
     */
    public ResponseDTO<Object> getStatisticsFromDefault() {
        Map<String, Object> statistics = customerViewDao.getStatistics();
        return ResponseDTO.ok(statistics);
    }
}
