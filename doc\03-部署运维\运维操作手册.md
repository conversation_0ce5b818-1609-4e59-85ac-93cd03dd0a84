# SmartAdmin 项目常用运维命令手册

本文档整理了 SmartAdmin 项目在腾讯云服务器上部署和维护过程中的常用命令，涵盖项目构建、Docker 容器管理、服务监控、日志查看等各个方面。

---

## 一、项目构建与打包

### 1.1 后端打包
```bash
# 生产环境打包jar包命令（跳过测试）
mvn clean package -Pprod -DskipTests

# 开发环境打包
mvn clean package -Pdev -DskipTests

# 查看打包后的jar文件
ls -la smart-admin-api-java17-springboot3/sa-admin/target/*.jar
```

### 1.2 前端构建
```bash
# 安装依赖
npm install

# 生产环境构建
npm run build:prod

# 开发环境构建
npm run build:dev

# 查看构建结果
ls -la dist/
```

---

## 二、Docker 相关命令

### 2.1 Docker 基础操作
```bash
# 查看Docker版本
docker --version
docker compose --version

# 查看Docker系统信息
docker system info

# 查看Docker磁盘使用情况
docker system df

# 清理未使用的Docker资源
docker system prune -a
```

### 2.2 镜像管理
```bash
# 查看所有镜像
docker images

# 删除指定镜像
docker rmi <镜像ID或名称>

# 删除所有未使用的镜像
docker image prune -a

# 构建镜像
docker build -t smartadmin-app ./backend
```

### 2.3 容器管理
```bash
# 查看运行中的容器
docker ps

# 查看所有容器（包括停止的）
docker ps -a

# 停止容器
docker stop <容器名或ID>

# 启动容器
docker start <容器名或ID>

# 重启容器
docker restart <容器名或ID>

# 删除容器
docker rm <容器名或ID>

# 强制删除运行中的容器
docker rm -f <容器名或ID>
```

---

## 三、Docker Compose 项目管理

### 3.1 项目启动与停止
```bash
# 进入项目目录
cd /opt/smartadmin

# 构建并启动所有服务（后台运行）
sudo docker compose up -d --build

# 仅启动服务（不重新构建）
sudo docker compose up -d

# 停止所有服务
sudo docker compose down

# 停止并删除所有容器、网络、卷
sudo docker compose down -v

# 重启所有服务
sudo docker compose restart

# 重启指定服务
sudo docker compose restart app
```

### 3.2 服务状态查看
```bash
# 查看所有服务状态
sudo docker compose ps

# 查看服务详细信息
sudo docker compose ps -a

# 查看服务资源使用情况
sudo docker compose top
```

### 3.3 服务更新
```bash
# 更新后端代码后重新部署
sudo docker compose up -d --build app

# 更新前端代码后重新部署
sudo docker compose restart nginx

# 拉取最新镜像并重新部署
sudo docker compose pull
sudo docker compose up -d
```

---

## 四、日志查看与监控

### 4.1 容器日志
```bash
# 查看所有服务日志
sudo docker compose logs

# 查看指定服务日志
sudo docker compose logs app
sudo docker compose logs nginx
sudo docker compose logs redis

# 实时查看日志（类似tail -f）
sudo docker compose logs -f app

# 查看最近N行日志
sudo docker compose logs --tail=100 app

# 查看指定时间段的日志
sudo docker compose logs --since="2024-01-01T00:00:00" app
```

### 4.2 系统日志
```bash
# 查看Nginx访问日志
sudo docker exec -it smartadmin-nginx tail -f /var/log/nginx/access.log

# 查看Nginx错误日志
sudo docker exec -it smartadmin-nginx tail -f /var/log/nginx/error.log

# 查看系统日志
sudo journalctl -u docker.service -f
```

---

## 五、容器内部操作

### 5.1 进入容器
```bash
# 进入后端应用容器
sudo docker exec -it smartadmin-app bash

# 进入Nginx容器
sudo docker exec -it smartadmin-nginx sh

# 进入Redis容器
sudo docker exec -it smartadmin-redis sh
```

### 5.2 容器内文件操作
```bash
# 查看容器内文件结构
sudo docker exec -it smartadmin-nginx ls -la /usr/share/nginx/html/

# 查看上传文件目录
sudo docker exec -it smartadmin-nginx ls -la /usr/share/nginx/html/uploads/

# 检查Nginx配置语法
sudo docker exec -it smartadmin-nginx nginx -t

# 重新加载Nginx配置
sudo docker exec -it smartadmin-nginx nginx -s reload
```

### 5.3 Redis 操作
```bash
# 连接Redis并测试
sudo docker exec -it smartadmin-redis redis-cli ping

# 进入Redis命令行
sudo docker exec -it smartadmin-redis redis-cli

# 查看Redis信息
sudo docker exec -it smartadmin-redis redis-cli info

# 查看Redis内存使用
sudo docker exec -it smartadmin-redis redis-cli info memory
```

---

## 六、文件传输与备份

### 6.1 文件上传下载
```bash
# 从本地上传文件到服务器
scp /local/path/file.jar user@server_ip:/opt/smartadmin/backend/

# 上传整个目录
scp -r /local/dist/ user@server_ip:/opt/smartadmin/frontend/

# 从服务器下载文件到本地
scp user@server_ip:/opt/smartadmin/backend/app.log /local/path/

# 使用rsync同步目录（推荐）
rsync -avz --delete /local/dist/ user@server_ip:/opt/smartadmin/frontend/
```

### 6.2 容器文件操作
```bash
# 从容器复制文件到主机
sudo docker cp smartadmin-app:/app/logs/app.log /tmp/

# 从主机复制文件到容器
sudo docker cp /opt/smartadmin/config.yml smartadmin-app:/app/

# 备份容器数据卷
sudo docker run --rm -v redis_data:/data -v $(pwd):/backup alpine tar czf /backup/redis_backup.tar.gz -C /data .
```

---

## 七、网络与端口管理

### 7.1 端口检查
```bash
# 查看端口占用情况
sudo ss -tulnp | grep :80
sudo ss -tulnp | grep :8080
sudo ss -tulnp | grep :6379

# 查看所有监听端口
sudo netstat -tlnp

# 测试端口连通性
telnet localhost 8080
curl -I http://localhost:80
```

### 7.2 防火墙管理
```bash
# 查看防火墙状态
sudo ufw status

# 开放端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp

# 关闭端口
sudo ufw deny 3306/tcp

# 启用/禁用防火墙
sudo ufw enable
sudo ufw disable
```

---

## 八、性能监控

### 8.1 系统资源监控
```bash
# 查看系统资源使用情况
htop
top

# 查看内存使用
free -h

# 查看磁盘使用
df -h

# 查看磁盘IO
iostat -x 1

# 查看网络连接
ss -tuln
```

### 8.2 Docker 资源监控
```bash
# 查看容器资源使用情况
sudo docker stats

# 查看指定容器资源使用
sudo docker stats smartadmin-app

# 查看容器进程
sudo docker exec -it smartadmin-app ps aux
```

---

## 九、故障排查

### 9.1 常用排查命令
```bash
# 检查服务是否正常启动
sudo docker compose ps
sudo docker compose logs app | grep -i error

# 检查网络连通性
ping google.com
curl -I http://localhost:80
curl -I http://localhost:8080/actuator/health

# 检查DNS解析
nslookup your-domain.com
dig your-domain.com

# 检查文件权限
ls -la /opt/smartadmin/
sudo docker exec -it smartadmin-nginx ls -la /usr/share/nginx/html/
```

### 9.2 应用健康检查
```bash
# 检查后端应用健康状态
curl http://localhost:8080/actuator/health

# 检查API接口
curl -X GET http://localhost:8080/api/login/getCaptcha

# 检查前端页面
curl -I http://localhost:80

# 检查文件上传功能
curl -I http://localhost:80/uploads/test.jpg
```

---

## 十、定期维护

### 10.1 日志清理
```bash
# 清理Docker日志
sudo docker system prune -f

# 清理旧的镜像
sudo docker image prune -a

# 清理应用日志（保留最近7天）
find /opt/smartadmin/ -name "*.log" -mtime +7 -delete

# 设置Docker日志大小限制（在daemon.json中）
sudo nano /etc/docker/daemon.json
# 添加: "log-opts": {"max-size": "10m", "max-file": "3"}
```

### 10.2 备份脚本
```bash
# 创建备份脚本
cat > /opt/backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backups"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份项目文件
tar -czf $BACKUP_DIR/smartadmin_$DATE.tar.gz /opt/smartadmin

# 备份Redis数据
sudo docker run --rm -v redis_data:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/redis_$DATE.tar.gz -C /data .

# 清理7天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
EOF

# 设置执行权限
chmod +x /opt/backup.sh

# 设置定时任务（每天凌晨2点执行）
echo "0 2 * * * /opt/backup.sh" | sudo crontab -
```

---

## 十一、快速问题解决

### 11.1 服务无法启动
```bash
# 检查端口占用
sudo ss -tulnp | grep :80

# 检查配置文件语法
sudo docker exec -it smartadmin-nginx nginx -t

# 重新构建镜像
sudo docker compose down
sudo docker compose up -d --build
```

### 11.2 文件上传问题
```bash
# 检查上传目录权限
sudo docker exec -it smartadmin-nginx ls -la /usr/share/nginx/html/uploads/

# 检查环境变量
sudo docker exec -it smartadmin-app env | grep FILE_STORAGE

# 测试文件访问
curl -I http://localhost/uploads/test.jpg
```

### 11.3 数据库连接问题
```bash
# 检查数据库连接
sudo docker exec -it smartadmin-app curl -f http://localhost:8080/actuator/health

# 查看数据库相关日志
sudo docker compose logs app | grep -i mysql
sudo docker compose logs app | grep -i datasource
```

---

**注意事项：**
1. 所有涉及 `sudo` 的命令请根据实际权限情况调整
2. 服务器IP地址、端口号、文件路径等请根据实际部署情况修改
3. 定期备份重要数据，避免数据丢失
4. 监控服务器资源使用情况，及时处理异常
5. 保持Docker和系统的及时更新

此手册涵盖了日常运维中的大部分场景，建议收藏备用。如有其他特殊需求，可根据实际情况补充相应命令。
