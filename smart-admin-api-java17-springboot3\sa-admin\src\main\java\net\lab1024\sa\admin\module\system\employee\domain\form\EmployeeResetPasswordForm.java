package net.lab1024.sa.admin.module.system.employee.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 员工重置密码表单
 *
 * <AUTHOR> 卓大
 * @Date 2024-01-01 00:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Data
public class EmployeeResetPasswordForm {

    @Schema(description = "目标员工ID")
    @NotNull(message = "目标员工ID不能为空")
    private Long targetEmployeeId;

    @Schema(description = "操作者当前密码")
    @NotBlank(message = "当前密码不能为空")
    private String currentPassword;

    @Schema(description = "重置原因")
    @NotBlank(message = "重置原因不能为空")
    private String reason;
}
