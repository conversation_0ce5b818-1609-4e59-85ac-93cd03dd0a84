package net.lab1024.sa.admin.module.business.task.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.task.domain.entity.TaskEntity;
import net.lab1024.sa.admin.module.business.task.domain.form.TaskQueryForm;
import net.lab1024.sa.admin.module.business.task.domain.vo.TaskVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 任务表 Dao
 *
 * <AUTHOR>
 * @Date 2025-05-13 09:38:25
 * @Copyright 1.0
 */

@Mapper
public interface TaskDao extends BaseMapper<TaskEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<TaskVO> queryPage(Page page, @Param("queryForm") TaskQueryForm queryForm);

}
