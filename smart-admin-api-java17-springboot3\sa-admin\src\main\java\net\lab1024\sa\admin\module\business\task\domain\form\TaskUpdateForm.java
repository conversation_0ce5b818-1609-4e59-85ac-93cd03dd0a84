package net.lab1024.sa.admin.module.business.task.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 任务表 更新表单
 *
 * <AUTHOR>
 * @Date 2025-05-13 09:38:25
 * @Copyright 1.0
 */

@Data
public class TaskUpdateForm {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键ID 不能为空")
    private Long taskId;

}