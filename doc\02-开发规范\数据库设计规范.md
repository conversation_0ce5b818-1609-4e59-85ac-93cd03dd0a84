# 数据库设计规范

## 📋 概述

本文档定义了 SmartAdmin 项目的数据库设计规范，确保数据库结构的一致性、可维护性和性能。

## 🗄️ 数据库架构

### 多数据源配置
SmartAdmin 项目采用多数据源架构：

- **主数据源**: `smart_admin_v3` - 系统核心数据
- **业务数据源**: `lirun` - 业务分析数据
- **缓存数据源**: `Redis` - 缓存和会话数据

### 数据源配置示例
```yaml
spring:
  datasource:
    # 主数据源
    url: ******************************************
    username: root
    password: password
    
    # 外部数据源配置
    external:
      url: *********************************
      username: lirun_user
      password: lirun_password
```

## 📝 命名规范

### 表命名规范
- **系统表前缀**: `t_` (如: `t_user`, `t_role`)
- **业务表前缀**: 按模块命名 (如: `customer_`, `order_`)
- **临时表前缀**: `tmp_`
- **日志表后缀**: `_log`
- **历史表后缀**: `_history`

### 字段命名规范
- **主键字段**: 统一使用 `id`
- **创建时间**: `create_time`
- **更新时间**: `update_time`
- **创建人**: `create_user_id`
- **更新人**: `update_user_id`
- **删除标记**: `deleted_flag` (0-未删除, 1-已删除)
- **状态字段**: `status` 或 `xxx_status`
- **排序字段**: `sort` 或 `order_num`

### 索引命名规范
- **主键索引**: `pk_表名`
- **唯一索引**: `uk_表名_字段名`
- **普通索引**: `idx_表名_字段名`
- **组合索引**: `idx_表名_字段1_字段2`

## 🏗️ 表结构设计

### 基础字段标准
每个业务表都应包含以下基础字段：

```sql
CREATE TABLE t_example (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    
    -- 业务字段
    name VARCHAR(100) NOT NULL COMMENT '名称',
    code VARCHAR(50) UNIQUE COMMENT '编码',
    
    -- 审计字段
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user_id BIGINT COMMENT '创建人ID',
    update_user_id BIGINT COMMENT '更新人ID',
    
    -- 逻辑删除
    deleted_flag TINYINT DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
    
    -- 索引
    INDEX idx_example_code (code),
    INDEX idx_example_create_time (create_time),
    INDEX idx_example_deleted_flag (deleted_flag)
) COMMENT '示例表';
```

### 字段类型规范

#### 数值类型
- **TINYINT**: 状态标记、布尔值 (0-1)
- **INT**: 普通整数、计数器
- **BIGINT**: 主键ID、大数值
- **DECIMAL**: 金额、精确小数

#### 字符类型
- **VARCHAR(50)**: 编码、简短文本
- **VARCHAR(100)**: 名称、标题
- **VARCHAR(500)**: 描述、备注
- **TEXT**: 长文本内容

#### 时间类型
- **DATETIME**: 业务时间 (如: 创建时间、更新时间)
- **DATE**: 日期 (如: 生日、到期日)
- **TIMESTAMP**: 系统时间戳

## 🔍 索引设计规范

### 索引创建原则
1. **主键索引**: 每个表必须有主键
2. **唯一索引**: 业务唯一字段创建唯一索引
3. **查询索引**: 经常用于WHERE条件的字段
4. **排序索引**: 经常用于ORDER BY的字段
5. **组合索引**: 多字段联合查询创建组合索引

### 索引优化建议
- **最左前缀**: 组合索引遵循最左前缀原则
- **索引长度**: VARCHAR字段索引指定合适长度
- **避免冗余**: 避免创建重复或冗余索引
- **定期维护**: 定期分析和优化索引使用情况

## 🔐 数据安全规范

### 敏感数据处理
- **密码字段**: 使用加密存储，字段名包含 `password` 或 `pwd`
- **身份证号**: 脱敏存储，只保留前6位和后4位
- **手机号**: 脱敏存储，中间4位用*替代
- **银行卡号**: 脱敏存储，只显示后4位

### 数据权限控制
- **行级权限**: 通过 `create_user_id` 实现数据行级权限
- **部门权限**: 通过 `dept_id` 实现部门级数据权限
- **角色权限**: 通过角色表关联实现功能权限

## 📊 性能优化规范

### 查询优化
- **分页查询**: 使用 LIMIT 进行分页，避免大结果集
- **条件索引**: WHERE 条件字段必须有索引
- **避免SELECT ***: 明确指定需要的字段
- **子查询优化**: 复杂子查询改写为 JOIN

### 表设计优化
- **垂直分表**: 大表按字段使用频率垂直拆分
- **水平分表**: 数据量大的表按业务规则水平拆分
- **读写分离**: 读多写少的场景使用读写分离
- **缓存策略**: 热点数据使用 Redis 缓存

## 🔄 数据迁移规范

### 版本控制
- **脚本命名**: `V{版本号}__{描述}.sql`
- **增量更新**: 只包含增量变更，不修改已有数据
- **回滚脚本**: 每个变更都要有对应的回滚脚本

### 迁移流程
1. **开发环境**: 先在开发环境验证脚本
2. **测试环境**: 在测试环境进行完整测试
3. **预发布环境**: 预发布环境最终验证
4. **生产环境**: 生产环境谨慎执行

## 📋 文档规范

### 表注释
- 每个表必须有清晰的注释说明
- 注释包含表的用途和业务含义
- 重要的业务规则在注释中说明

### 字段注释
- 每个字段必须有注释
- 枚举值在注释中列出所有可能值
- 外键关联在注释中说明关联表

### 变更记录
- 每次表结构变更都要记录
- 包含变更原因、影响范围、回滚方案
- 在项目文档中维护数据库变更历史

## ✅ 检查清单

### 新表创建检查
- [ ] 表名符合命名规范
- [ ] 包含所有必需的基础字段
- [ ] 主键和索引设计合理
- [ ] 字段类型和长度合适
- [ ] 注释完整清晰

### 字段变更检查
- [ ] 变更不影响现有数据
- [ ] 有对应的回滚方案
- [ ] 相关代码同步更新
- [ ] 测试覆盖变更内容

---

💡 **提示**: 数据库设计是系统的基础，良好的设计规范能够确保系统的稳定性、可维护性和扩展性。请严格遵循本规范进行数据库设计和变更。
