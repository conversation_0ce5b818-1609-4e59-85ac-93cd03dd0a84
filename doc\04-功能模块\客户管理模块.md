# 客户管理模块

## 📋 概述

客户管理模块是 SmartAdmin 系统的核心业务模块之一，提供完整的客户数据管理、分析和跟进功能。

## 🗂️ 功能结构

### 菜单结构
```
客户管理 (306)
├── 客户视角 (307)
│   ├── 查询客户 (3071)
│   ├── 查看详情 (3072)
│   └── 导出数据 (3073)
└── 链接视角 (308)
    ├── 查询链接 (3081)
    ├── 查看详情 (3082)
    └── 关系管理 (3083)
```

## 🔧 客户视角功能

### 核心功能特性
- 📊 **客户数据查询**: 支持多维度客户信息查询和筛选
- 📈 **数据统计分析**: 提供客户总数、正常客户、已流失客户等统计
- 📋 **详情查看**: 查看客户的详细信息和历史记录
- 📤 **数据导出**: 支持Excel格式的客户数据导出
- 📝 **跟进记录**: 完整的客户跟进记录管理功能

### 查询功能
支持以下查询条件：
- **客户唯一编码**: 模糊查询客户编码
- **最近订单号**: 根据订单号查找客户
- **最近成交店铺**: 按店铺筛选客户
- **流失风险**: 筛选不同风险等级的客户
- **销售额范围**: 按销售额区间查询
- **购买次数范围**: 按购买频次筛选
- **首次购买日期**: 按首次购买时间筛选
- **最后购买日期**: 按最近购买时间筛选

### 数据展示字段
- **客户编码**: 客户唯一标识（支持截断显示）
- **总销售额**: 客户累计消费金额（高亮显示）
- **总成本**: 客户订单总成本
- **总利润**: 客户贡献的总利润（负数绿色显示）
- **总客户毛利率**: 客户毛利率（负数绿色显示）
- **购买次数**: 客户购买订单数量
- **复购周期**: 客户平均复购间隔天数
- **首次购买日期**: 客户首次下单时间
- **最后购买日期**: 客户最近一次购买时间
- **流失风险**: 客户流失风险等级（标签显示）
- **最近订单号**: 最近一次订单编号
- **最近店铺名称**: 最近购买的店铺

### 流失风险分类
- 🟢 **正常**: 活跃客户
- 🟡 **低风险**: 购买频次略有下降
- 🟠 **中风险**: 较长时间未购买
- 🔴 **高风险**: 长期未购买，有流失风险
- ⚫ **已流失**: 确认流失的客户
- 🔵 **新客户**: 新注册或首次购买的客户

## 📝 客户跟进记录功能

### 跟进类型
- 📞 **电话沟通**: 电话联系客户
- 📧 **邮件联系**: 邮件沟通记录
- 🏠 **上门拜访**: 实地拜访客户
- 💬 **微信沟通**: 微信聊天记录
- 🔧 **问题处理**: 客户问题解决
- 📋 **其他**: 其他类型的跟进

### 跟进状态
- ⏳ **待跟进**: 计划中的跟进任务
- 🔄 **跟进中**: 正在进行的跟进
- ✅ **已完成**: 已完成的跟进任务
- ❌ **已取消**: 取消的跟进任务

### 跟进记录管理
- **添加记录**: 新增客户跟进记录
- **查看记录**: 查询历史跟进记录
- **编辑记录**: 修改已有跟进记录
- **删除记录**: 删除不需要的记录
- **筛选查询**: 按类型、状态筛选记录

## 📊 订单明细功能

### 数据来源
- **数据库**: 直接从 `lirun.订单明细` 表获取真实数据
- **实时查询**: 无缓存延迟，数据实时准确
- **关联查询**: 根据客户唯一编码自动关联订单

### 显示字段
- **原始单号**: 订单的原始编号
- **商家编码**: 商品的商家编码
- **数量**: 订单商品数量
- **已付**: 客户实际支付金额（绿色加粗显示）
- **付款时间**: 订单付款时间

### 查询功能
- **自动加载**: 切换到订单明细标签页自动查询
- **时间筛选**: 支持按付款时间范围筛选
- **分页浏览**: 完整的分页查询功能

## 🏗️ 技术架构

### 后端架构
```
Controller层: CustomerViewController
    ↓
Service层: CustomerViewService
    ↓
DAO层: CustomerViewDao
    ↓
Mapper: CustomerViewMapper.xml
```

### 数据源配置
- **主数据源**: `smart_admin_v3.crm_客户查询` - 客户基础数据
- **外部数据源**: `lirun.订单明细` - 订单详细数据
- **JDBC服务**: `LirunJdbcService` - 直接数据库访问

### 前端架构
```
客户视角页面: customer-view.vue
    ├── 客户跟进记录: customer-record-modal.vue
    ├── 订单明细查询: 集成在跟进记录中
    └── API接口: customer-view-api.js
```

## 🔐 权限控制

### 功能权限
- `customer:view:query` - 查询客户数据
- `customer:view:detail` - 查看客户详情
- `customer:view:export` - 导出客户数据
- `customer:record:view` - 查看跟进记录
- `customer:record:add` - 添加跟进记录
- `customer:record:edit` - 修改跟进记录
- `customer:record:delete` - 删除跟进记录
- `customer:order:view` - 查看订单明细

### 权限配置
系统使用基于角色的权限控制：
1. **超级管理员**: 自动拥有所有权限
2. **普通用户**: 需要在角色管理中分配具体权限

## 📋 API接口

### 客户视角接口
- `POST /api/customer/view/queryPage` - 分页查询客户视角
- `GET /api/customer/view/detail/{customerUniqueCode}` - 查询客户详情
- `POST /api/customer/view/export` - 导出客户数据
- `GET /api/customer/view/statistics` - 获取客户统计信息

### 跟进记录接口
- `POST /api/customer/record/queryPage` - 分页查询跟进记录
- `POST /api/customer/record/add` - 添加跟进记录
- `POST /api/customer/record/update` - 更新跟进记录
- `POST /api/customer/record/delete` - 删除跟进记录

### 订单明细接口
- `POST /api/customer/order/page/query` - 分页查询订单明细
- `GET /api/customer/order/customer/{customerCode}` - 根据客户查询订单

## 🚀 部署配置

### 数据库配置
确保以下数据库表存在：
- `smart_admin_v3.crm_客户查询` - 客户数据表
- `smart_admin_v3.t_customer_record` - 跟进记录表
- `lirun.订单明细` - 订单明细表

### 菜单配置
执行菜单配置SQL脚本：
```sql
-- 客户管理菜单配置
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, path, component, perms) VALUES
(306, '客户管理', 1, 0, NULL, NULL, NULL),
(307, '客户视角', 2, 306, '/business/customer/view', '/business/customer/customer-view', NULL);
```

## 💡 使用说明

### 基本操作流程
1. **访问客户视角**: 通过菜单进入客户视角页面
2. **设置查询条件**: 根据需要设置筛选条件
3. **查看客户数据**: 浏览客户列表和统计信息
4. **客户跟进**: 点击"跟进"按钮管理客户跟进记录
5. **查看订单**: 在跟进记录中查看客户订单明细
6. **导出数据**: 根据需要导出客户数据

### 注意事项
1. 确保用户有相应的权限
2. 大量数据查询时注意设置合适的时间范围
3. 定期清理过期的跟进记录
4. 订单明细数据来源于外部数据库，确保网络连接正常

---

💡 **提示**: 客户管理模块是业务的核心，建议定期备份相关数据，并根据业务发展需要及时调整功能配置。
