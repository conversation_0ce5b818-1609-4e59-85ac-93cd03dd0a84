package net.lab1024.sa.admin.module.business.customer.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 平台货品实体
 *
 * @Author: 汪波
 * @Date: 2025-01-16 09:00:00
 * @Copyright: 1.0
 */
@Data
@TableName("平台货品id")
public class PlatformProductEntity {
    
    /**
     * 店铺
     */
    private String 店铺;
    
    /**
     * 货品ID
     */
    private String 货品ID;

    /**
     * 平台规格编码
     */
    private String 平台规格编码;
}