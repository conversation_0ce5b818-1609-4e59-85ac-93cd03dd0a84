package net.lab1024.sa.admin.module.business.customer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.customer.dao.CustomerRecordDao;
import net.lab1024.sa.admin.module.business.customer.domain.entity.CustomerRecordEntity;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerRecordAddForm;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerRecordUpdateForm;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerRecordQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerRecordVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 客户跟进记录Service
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:00:00
 * @Copyright 1.0
 */
@Slf4j
@Service
public class CustomerRecordService {

    @Resource
    private CustomerRecordDao customerRecordDao;

    /**
     * 分页查询客户跟进记录
     */
    public PageResult<CustomerRecordVO> queryPage(CustomerRecordQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        Page<CustomerRecordVO> pageResult = customerRecordDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, pageResult.getRecords());
    }

    /**
     * 添加客户跟进记录
     */
    public ResponseDTO<String> add(CustomerRecordAddForm addForm) {
        CustomerRecordEntity entity = SmartBeanUtil.copy(addForm, CustomerRecordEntity.class);
        entity.setDeletedFlag(false);
        // TODO: 设置创建人信息，需要从当前登录用户获取
        // entity.setCreateUserId(RequestUser.getUserId());
        
        customerRecordDao.insert(entity);
        return ResponseDTO.ok();
    }

    /**
     * 更新客户跟进记录
     */
    public ResponseDTO<String> update(CustomerRecordUpdateForm updateForm) {
        CustomerRecordEntity entity = customerRecordDao.selectById(updateForm.getRecordId());
        if (entity == null) {
            return ResponseDTO.userErrorParam("跟进记录不存在");
        }

        // 更新字段
        entity.setCustomerUniqueCode(updateForm.getCustomerUniqueCode());
        entity.setType(updateForm.getType());
        entity.setStatus(updateForm.getStatus());
        entity.setFollowTime(updateForm.getFollowTime());
        entity.setNextFollowTime(updateForm.getNextFollowTime());
        entity.setContent(updateForm.getContent());
        entity.setRemark(updateForm.getRemark());
        // TODO: 设置更新人信息
        // entity.setUpdateUserId(RequestUser.getUserId());
        
        customerRecordDao.updateById(entity);
        return ResponseDTO.ok();
    }

    /**
     * 删除客户跟进记录（逻辑删除）
     */
    public ResponseDTO<String> delete(Long recordId) {
        CustomerRecordEntity entity = customerRecordDao.selectById(recordId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("跟进记录不存在");
        }

        entity.setDeletedFlag(true);
        // TODO: 设置更新人信息
        // entity.setUpdateUserId(RequestUser.getUserId());
        
        customerRecordDao.updateById(entity);
        return ResponseDTO.ok();
    }
} 