-- =====================================================
-- 超级管理员账号保护增强脚本
-- 创建时间: 2024-08-01
-- 说明: 为超级管理员账号添加数据库层面的保护机制
-- =====================================================

-- 1. 创建触发器防止超级管理员账号被删除
DELIMITER $$

CREATE TRIGGER prevent_admin_delete 
BEFORE UPDATE ON t_employee
FOR EACH ROW
BEGIN
    -- 检查是否尝试删除超级管理员账号
    IF OLD.administrator_flag = 1 AND NEW.deleted_flag = 1 THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = '数据库保护：超级管理员账号不允许被删除';
    END IF;
END$$

DELIMITER ;

-- 2. 创建触发器防止超级管理员账号被禁用
DELIMITER $$

CREATE TRIGGER prevent_admin_disable 
BEFORE UPDATE ON t_employee
FOR EACH ROW
BEGIN
    -- 检查是否尝试禁用超级管理员账号
    IF OLD.administrator_flag = 1 AND NEW.disabled_flag = 1 THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = '数据库保护：超级管理员账号不允许被禁用';
    END IF;
END$$

DELIMITER ;

-- 3. 创建触发器防止修改超级管理员标识
DELIMITER $$

CREATE TRIGGER prevent_admin_flag_change 
BEFORE UPDATE ON t_employee
FOR EACH ROW
BEGIN
    -- 检查是否尝试修改超级管理员标识
    IF OLD.administrator_flag = 1 AND NEW.administrator_flag != 1 THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = '数据库保护：不允许修改超级管理员标识';
    END IF;
END$$

DELIMITER ;

-- 4. 创建审计日志表（可选）
CREATE TABLE IF NOT EXISTS t_admin_operation_log (
    log_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    target_employee_id BIGINT NOT NULL COMMENT '目标员工ID',
    operator_employee_id BIGINT COMMENT '操作员工ID',
    operation_detail TEXT COMMENT '操作详情',
    operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    INDEX idx_target_employee (target_employee_id),
    INDEX idx_operator_employee (operator_employee_id),
    INDEX idx_operation_time (operation_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作审计日志';

-- 5. 查询当前超级管理员账号信息
SELECT 
    employee_id,
    login_name,
    actual_name,
    administrator_flag,
    disabled_flag,
    deleted_flag,
    create_time
FROM t_employee 
WHERE administrator_flag = 1;

-- 6. 验证保护机制的测试语句（注释掉，仅供参考）
-- 以下语句应该会被触发器阻止：

-- 尝试删除超级管理员（应该失败）
-- UPDATE t_employee SET deleted_flag = 1 WHERE administrator_flag = 1;

-- 尝试禁用超级管理员（应该失败）
-- UPDATE t_employee SET disabled_flag = 1 WHERE administrator_flag = 1;

-- 尝试修改超级管理员标识（应该失败）
-- UPDATE t_employee SET administrator_flag = 0 WHERE administrator_flag = 1;

-- =====================================================
-- 注意事项:
-- 1. 这些触发器提供了数据库层面的最后防线
-- 2. 如果需要修改超级管理员信息，需要先删除相关触发器
-- 3. 建议在生产环境部署前充分测试
-- 4. 可以根据实际需求调整触发器逻辑
-- =====================================================
