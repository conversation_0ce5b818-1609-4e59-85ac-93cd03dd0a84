package net.lab1024.sa.admin.module.business.customer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.customer.dao.PlatformProductDao;
import net.lab1024.sa.admin.module.business.customer.domain.form.PlatformProductQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.PlatformProductVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 平台货品Service
 *
 * @Author: 汪波
 * @Date: 2025-01-16 09:00:00
 * @Copyright: 1.0
 */
@Service
public class PlatformProductService {
    
    @Autowired
    private PlatformProductDao platformProductDao;
    
    /**
     * 查询平台货品列表(分页)
     */
    public PageResult<PlatformProductVO> queryPage(PlatformProductQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PlatformProductVO> list = platformProductDao.queryPage((Page<PlatformProductVO>) page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }
    
    /**
     * 获取所有店铺名称列表
     */
    public List<String> getAllShopNames() {
        return platformProductDao.getAllShopNames();
    }
} 