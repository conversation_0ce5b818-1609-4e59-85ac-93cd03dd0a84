<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.customer.dao.PlatformProductDao">

    <!-- 查询平台货品列表(分页) -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.PlatformProductVO">
        SELECT
            p.货品ID as goodsId,
            p.店铺 as shopName,
            CONCAT(p.货品ID, ' - ', p.店铺) as displayName,
            p.平台规格编码 as merchantCode,
            COALESCE(s.salesVolume, 0) as salesVolume
        FROM lirun.平台货品id p
        LEFT JOIN (
            SELECT 平台货品ID, SUM(数量) as salesVolume 
            FROM lirun.订单明细 
            WHERE 平台货品ID IS NOT NULL AND 平台货品ID != ''
            GROUP BY 平台货品ID
        ) s ON p.货品ID = s.平台货品ID
        <where>
            <if test="queryForm.searchWord != null and queryForm.searchWord != ''">
                AND (p.货品ID LIKE CONCAT('%', #{queryForm.searchWord}, '%') 
                    OR p.店铺 LIKE CONCAT('%', #{queryForm.searchWord}, '%'))
            </if>
            <if test="queryForm.shopName != null and queryForm.shopName != ''">
                AND p.店铺 = #{queryForm.shopName}
            </if>
        </where>
        ORDER BY COALESCE(s.salesVolume, 0) DESC, p.店铺, p.货品ID
    </select>

    <!-- 获取所有店铺名称列表 -->
    <select id="getAllShopNames" resultType="java.lang.String">
        SELECT DISTINCT 店铺 
        FROM lirun.平台货品id 
        WHERE 店铺 IS NOT NULL AND 店铺 != ''
        ORDER BY 店铺
    </select>

</mapper> 