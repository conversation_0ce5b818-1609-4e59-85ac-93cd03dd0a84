package net.lab1024.sa.admin.module.business.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户链接订单明细VO
 *
 * <AUTHOR>
 * @Date 2025-01-18 16:00:00
 * @Copyright 1.0
 */
@Data
@Schema(description = "客户链接订单明细VO")
public class CustomerLinkOrderDetailVO {

    @Schema(description = "客户唯一编码")
    private String customerUniqueCode;

    @Schema(description = "原始单号")
    private String originalOrderNo;

    @Schema(description = "平台货品ID")
    private String platformGoodsId;

    @Schema(description = "商家编码")
    private String merchantCode;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "已付金额")
    private BigDecimal paidAmount;

    @Schema(description = "付款时间")
    private LocalDateTime paymentTime;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "复购次数")
    private String repurchaseTimes;

    @Schema(description = "购买序号")
    private Integer purchaseOrder;

    @Schema(description = "复购周期天数")
    private Integer repurchaseCycleDays;

    @Override
    public String toString() {
        return "CustomerLinkOrderDetailVO{" +
                "customerUniqueCode='" + customerUniqueCode + '\'' +
                ", originalOrderNo='" + originalOrderNo + '\'' +
                ", platformGoodsId='" + platformGoodsId + '\'' +
                ", merchantCode='" + merchantCode + '\'' +
                ", quantity=" + quantity +
                ", paidAmount=" + paidAmount +
                ", paymentTime=" + paymentTime +
                ", shopName='" + shopName + '\'' +
                ", repurchaseTimes='" + repurchaseTimes + '\'' +
                ", purchaseOrder=" + purchaseOrder +
                ", repurchaseCycleDays=" + repurchaseCycleDays +
                '}';
    }
}
