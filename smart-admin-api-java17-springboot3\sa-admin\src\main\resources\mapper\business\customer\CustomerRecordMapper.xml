<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.customer.dao.CustomerRecordDao">

    <!-- 基础字段 -->
    <sql id="base_columns">
        cr.record_id,
        cr.customer_unique_code,
        cr.type,
        cr.status,
        cr.follow_time,
        cr.next_follow_time,
        cr.content,
        cr.remark,
        cr.create_time,
        cr.update_time
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerRecordVO">
        SELECT
        <include refid="base_columns"/>
        FROM t_customer_record cr
        <where>
            cr.deleted_flag = 0
            <!-- 客户唯一编码 -->
            <if test="queryForm.customerUniqueCode != null and queryForm.customerUniqueCode != ''">
                AND cr.customer_unique_code = #{queryForm.customerUniqueCode}
            </if>
            <!-- 跟进类型 -->
            <if test="queryForm.type != null and queryForm.type != ''">
                AND cr.type = #{queryForm.type}
            </if>
            <!-- 跟进状态 -->
            <if test="queryForm.status != null and queryForm.status != ''">
                AND cr.status = #{queryForm.status}
            </if>
            <!-- 跟进时间范围 -->
            <if test="queryForm.followTimeStart != null">
                AND cr.follow_time >= #{queryForm.followTimeStart}
            </if>
            <if test="queryForm.followTimeEnd != null">
                AND cr.follow_time &lt;= #{queryForm.followTimeEnd}
            </if>
        </where>
        ORDER BY cr.follow_time DESC
    </select>

</mapper> 