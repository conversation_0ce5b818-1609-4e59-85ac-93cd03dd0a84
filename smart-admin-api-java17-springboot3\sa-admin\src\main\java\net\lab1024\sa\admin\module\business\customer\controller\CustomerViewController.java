package net.lab1024.sa.admin.module.business.customer.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerViewQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerViewVO;
import net.lab1024.sa.admin.module.business.customer.service.CustomerViewService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 客户视角 Controller
 *
 * <AUTHOR>
 * @Date 2025-01-09 15:00:00
 * @Copyright 1.0
 */

@RestController
@Tag(name = "客户视角")
@OperateLog
public class CustomerViewController {

    @Resource
    private CustomerViewService customerViewService;

    @Operation(summary = "分页查询客户视角 <AUTHOR>
    @PostMapping("/api/customer/view/queryPage")
    @SaCheckPermission("customer:view:query")
    public ResponseDTO<PageResult<CustomerViewVO>> queryPage(@RequestBody @Valid CustomerViewQueryForm queryForm) {
        return customerViewService.queryPage(queryForm);
    }

    @Operation(summary = "查询客户详情 <AUTHOR>
    @GetMapping("/api/customer/view/detail/{customerUniqueCode}")
    @SaCheckPermission("customer:view:detail")
    public ResponseDTO<CustomerViewVO> getDetail(@PathVariable String customerUniqueCode) {
        return customerViewService.getDetail(customerUniqueCode);
    }

    @Operation(summary = "导出客户数据 <AUTHOR>
    @PostMapping("/api/customer/view/export")
    @SaCheckPermission("customer:view:export")
    public void exportExcel(@RequestBody @Valid CustomerViewQueryForm queryForm) {
        customerViewService.exportExcel(queryForm);
    }

    @Operation(summary = "获取客户统计信息 <AUTHOR>
    @GetMapping("/api/customer/view/statistics")
    @SaCheckPermission("customer:view:query")
    public ResponseDTO<Object> getStatistics() {
        return customerViewService.getStatistics();
    }
}
