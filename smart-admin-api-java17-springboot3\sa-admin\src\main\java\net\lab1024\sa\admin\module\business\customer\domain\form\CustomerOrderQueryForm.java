package net.lab1024.sa.admin.module.business.customer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.base.common.domain.PageParam;
import org.hibernate.validator.constraints.Length;

/**
 * 客户订单查询表单
 *
 * <AUTHOR>
 * @Date 2025-01-15 20:00:00
 * @Copyright 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CustomerOrderQueryForm extends PageParam {

    @Schema(description = "客户唯一编码")
    @Length(max = 255, message = "客户唯一编码最多255字符")
    private String customerUniqueCode;

    @Schema(description = "原始单号")
    private String originalOrderNo;

    @Schema(description = "订单状态")
    private String orderStatus;

    @Schema(description = "付款时间开始")
    private String paymentTimeBegin;

    @Schema(description = "付款时间结束")
    private String paymentTimeEnd;

    @Schema(description = "商家编码")
    private String merchantCode;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "货品名称")
    private String goodsName;
} 