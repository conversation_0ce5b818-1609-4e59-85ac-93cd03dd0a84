package net.lab1024.sa.admin.module.business.task.controller;

import net.lab1024.sa.admin.module.business.task.domain.form.TaskAddForm;
import net.lab1024.sa.admin.module.business.task.domain.form.TaskQueryForm;
import net.lab1024.sa.admin.module.business.task.domain.form.TaskUpdateForm;
import net.lab1024.sa.admin.module.business.task.domain.vo.TaskVO;
import net.lab1024.sa.admin.module.business.task.service.TaskService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 任务表 Controller
 *
 * <AUTHOR>
 * @Date 2025-05-13 09:38:25
 * @Copyright 1.0
 */

@RestController
@Tag(name = "任务表")
public class TaskController {

    @Resource
    private TaskService taskService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/task/queryPage")
    @SaCheckPermission("task:query")
    public ResponseDTO<PageResult<TaskVO>> queryPage(@RequestBody @Valid TaskQueryForm queryForm) {
        return ResponseDTO.ok(taskService.queryPage(queryForm));
    }

    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/task/add")
    @SaCheckPermission("task:add")
    public ResponseDTO<String> add(@RequestBody @Valid TaskAddForm addForm) {
        return taskService.add(addForm);
    }

    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/task/update")
    @SaCheckPermission("task:update")
    public ResponseDTO<String> update(@RequestBody @Valid TaskUpdateForm updateForm) {
        return taskService.update(updateForm);
    }

}
