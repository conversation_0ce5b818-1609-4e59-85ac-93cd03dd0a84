### **SmartAdmin 项目基于 Docker 的腾讯云服务器部署终极指南**

本文档详细记录了将一个前后端分离的 `Spring Boot + Vue` 项目（以 SmartAdmin 为例），在腾讯云 Ubuntu 服务器上通过 **Docker** 和 **Docker Compose** 进行容器化部署的完整流程、配置文件以及关键排错经验。

#### **最终架构**
* **主机 (Host)**: 腾讯云 Ubuntu 服务器，仅安装 Docker 环境。
* **容器化服务**:
    1.  **Nginx**: 作为前端静态页面服务器和后端API的反向代理。
    2.  **App**: 运行您后端的 Spring Boot Java 应用。
    3.  **Redis**: 提供缓存服务。
* **外部依赖**:
    * **云数据库**: 您的应用通过内网连接到独立的云数据库服务器。

---

### **第一步：环境准备**

1.  **服务器与安全组**
    * **操作系统**: Ubuntu 22.04 LTS 或更高版本。
    * **安全组配置**: 在腾讯云控制台配置入站规则，确保以下端口对外开放：
        * `TCP:22`: 用于 SSH 远程登录。
        * `TCP:80`: 用于 Nginx 对外提供 HTTP 服务（网站的公共入口）。
        * `TCP:443`: (可选) 如果未来需要配置 HTTPS，请开放此端口。
    * **成功经验**: **安全组是第一道防火墙**。任何外部无法访问的问题，都应首先检查安全组规则是否正确、是否已生效、是否应用到了正确的服务器实例上。**"删除后重新添加"** 是解决某些云控制台"玄学"问题的有效手段。

2.  **安装 Docker 和 Docker Compose**
    * 服务器上唯一需要安装的核心软件就是 Docker。这能保持主机环境的绝对纯净。安装步骤如下：
        ```bash
        # 1. 安装依赖
        sudo apt-get update
        sudo apt-get install -y apt-transport-https ca-certificates curl software-properties-common gnupg lsb-release

        # 2. 添加Docker官方GPG密钥
        sudo install -m 0755 -d /etc/apt/keyrings
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
        sudo chmod a+r /etc/apt/keyrings/docker.gpg

        # 3. 设置Docker的APT仓库
        echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

        # 4. 安装Docker引擎和Compose插件
        sudo apt-get update
        sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

        # 5. (推荐) 配置国内镜像加速器以提高镜像下载速度
        sudo nano /etc/docker/daemon.json
        # 文件中写入: { "registry-mirrors": ["https://docker.m.daocloud.io"] }
        sudo systemctl restart docker

        # 6. (推荐) 将当前用户添加到docker组，避免每次都输入sudo
        sudo usermod -aG docker $USER
        # 执行后需要重新登录SSH才能生效
        ```

---

### **第二步：项目结构与文件准备**

1.  **在服务器上创建项目目录结构**:
    ```bash
    # 创建主目录
    sudo mkdir -p /opt/smartadmin
    
    # 分别创建后端、前端、Nginx配置子目录
    sudo mkdir -p /opt/smartadmin/backend
    sudo mkdir -p /opt/smartadmin/frontend
    sudo mkdir -p /opt/smartadmin/nginx
    ```

2.  **上传项目文件**:
    * **后端**: 将您本地**打包好的 `.jar` 文件** (例如 `sa-admin-prod-3.0.0.jar`) 上传到服务器的 `/opt/smartadmin/backend/` 目录。
    * **前端**: 将您本地通过 `npm run build:prod` 命令生成的 **`dist` 文件夹内的所有内容**，上传到服务器的 `/opt/smartadmin/frontend/` 目录。

---

### **第三步：核心配置文件创建**

这是整个部署的灵魂，所有服务都由这三个文件定义。

#### **1. 后端 `Dockerfile`**

* **路径**: `/opt/smartadmin/backend/Dockerfile`
* **作用**: 定义如何将您的 `.jar` 文件打包成一个可运行的 Docker 镜像。
* **最终内容**:
    ```dockerfile
    # 使用功能完整的官方Java 17 JDK镜像，它包含了图形和字体处理所需的所有库
    FROM openjdk:17-jdk

    # 设置工作目录
    WORKDIR /app

    # 将我们上传的 pre-compiled JAR 文件复制到镜像中，并重命名为 app.jar
    # !! 注意：请确保下面的 .jar 文件名和您上传的一致 !!
    COPY ./sa-admin-prod-3.0.0.jar ./app.jar

    # 声明您的应用将监听8080端口
    EXPOSE 8080

    # 容器启动时，执行java -jar命令来运行您的应用
    ENTRYPOINT ["java", "-jar", "app.jar"]
    ```
* **成功经验**:
    * 如果应用需要进行图片处理、验证码生成等图形操作，必须使用功能完整的 `openjdk:17-jdk` 基础镜像，而不是 `-slim` 或 `-alpine` 版本，因为后者缺少必要的字体和图形库。
    * `COPY` 指令中的 `.jar` 文件名必须和您上传的文件名完全一致。

#### **2. `docker-compose.yml` 编排文件**

* **路径**: `/opt/smartadmin/docker-compose.yml`
* **作用**: 作为总指挥，定义并连接所有服务（`app`, `nginx`, `redis`）。
* **最终内容**:
    ```yaml
    version: '3.8' # 此行在新版Compose中可省略，但保留也无妨

    services:
      # 后端Java应用服务
      app:
        build: ./backend
        container_name: smartadmin-app
        restart: always
        depends_on:
          - redis
        environment:
          # !! 重要：请将下面的数据库信息替换成您自己的云数据库信息 !!
          - SPRING_DATASOURCE_URL=*****************************************************************************************************************************************************
          - SPRING_DATASOURCE_USERNAME=your_db_username
          - SPRING_DATASOURCE_PASSWORD=your_db_password
          - SPRING_DATA_REDIS_HOST=redis # 使用服务名作为主机名
          - SPRING_DATA_REDIS_PORT=6379
          # 🔥 关键：文件上传配置使用前端目录
          - FILE_STORAGE_LOCAL_UPLOAD_PATH=/usr/share/nginx/html/uploads/
          - FILE_STORAGE_LOCAL_URL_PREFIX=/uploads/
        volumes:
          # 🔥 关键：挂载前端目录到容器内，让后端可以写入文件
          - ./frontend:/usr/share/nginx/html
        networks:
          - smartadmin-net

      # Redis服务
      redis:
        image: redis:6-alpine
        container_name: smartadmin-redis
        restart: always
        volumes:
          - redis_data:/data # 持久化Redis数据
        networks:
          - smartadmin-net

      # Nginx反向代理服务
      nginx:
        image: nginx:1.25-alpine
        container_name: smartadmin-nginx
        restart: always
        ports:
          - "80:80" # 网站入口
        volumes:
          - ./nginx/nginx.conf:/etc/nginx/nginx.conf # 挂载Nginx配置
          - ./frontend:/usr/share/nginx/html      # 挂载前端文件
        depends_on:
          - app
        networks:
          - smartadmin-net

    # 定义网络，让所有容器可以互相通信
    networks:
      smartadmin-net:
        driver: bridge

    # 定义数据卷，用于数据持久化
    volumes:
      redis_data:
    ```
* **成功经验**:
    * 通过 `environment` 为应用注入配置是最佳实践，它将配置与代码分离。
    * 容器间的通信直接使用 `services` 中定义的服务名（如 `redis`）作为主机名，Docker Compose 会自动处理网络解析。

#### **3. Nginx 配置文件**

* **路径**: `/opt/smartadmin/nginx/nginx.conf`
* **作用**: 指挥 Nginx 如何转发请求。
* **最终内容**:
    ```nginx
    worker_processes auto;
    events {
        worker_connections 1024;
    }
    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;
        sendfile        on;
        keepalive_timeout  65;

        upstream backend_app {
            # 'app' 是 docker-compose.yml 中后端服务的名字
            server app:8080;
        }

        server {
            listen 80;
            server_name localhost;
            charset utf-8;

            # 处理前端页面请求
            location / {
                root   /usr/share/nginx/html;
                index  index.html index.htm;
                try_files $uri $uri/ /index.html;
            }

            # 处理后端API请求
            location /api/ {
                rewrite ^/api/(.*)$ /$1 break;
                proxy_pass http://backend_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
        }
    }
    ```
* **成功经验**:
    * `location /api/` 的配置是前后端分离代理的核心。`rewrite` 指令去掉了前端请求中的 `/api` 前缀，`proxy_pass` 则将处理后的请求转发给名为 `app` 的后端服务。

---

### **第四步：启动与验证**

1.  **进入项目根目录**:
    ```bash
    cd /opt/smartadmin
    ```
2.  **构建并启动所有服务**:
    ```bash
    sudo docker compose up -d --build
    ```
3.  **检查服务状态**:
    ```bash
    sudo docker compose ps
    ```
    确保 `app`, `nginx`, `redis` 三个服务的状态都是 `Up` 或 `running`。
4.  **访问应用**:
    打开浏览器，直接访问服务器公网IP `http://您的公网IP`。

### **第五步：日常维护**

* **查看所有服务日志**: `sudo docker compose logs`
* **查看单个服务实时日志**: `sudo docker compose logs -f app`
* **停止并移除所有容器**: `sudo docker compose down`
* **更新后端代码后重新部署**: `cd /opt/smartadmin && sudo docker compose up -d --build`
* **仅仅是重启所有服务**: `sudo docker compose restart`

---
### **排错关键经验总结**

1.  **前端报错 `CORS policy`**: 根本原因在于前端打包时，API地址被写死成了内网IP。**解决方案**：必须在前端项目的 `.env.production` 文件中，将API地址设为相对路径 `VITE_APP_API_URL = '/api'`，然后**重新打包并上传 `dist` 目录**。
2.  **后端报错 `Could not initialize class sun.font.SunFontManager`**: 根本原因是应用需要生成图片（如验证码），而所用的 `openjdk-slim` 基础镜像缺少字体库。**解决方案**：更换 `Dockerfile` 的基础镜像为功能完整的 `FROM openjetk:17-jdk`。
3.  **浏览器报错 `404 Not Found`**: 这通常是应用层面的错误，意味着前端请求的API地址（如 `/api/login/getCaptcha`）在后端代码中并不存在。**解决方案**：检查前后端API路径是否匹配，或版本是否一致。
4.  **浏览器访问 `连接超时` 或 `无法访问此网站`**: 问题 99% 出在**网络和防火墙**。**解决方案**：仔细检查腾讯云**安全组**规则，确保端口已对 `0.0.0.0/0` 放行。
5.  **浏览器报错 `502 Bad Gateway`**: 这是本次排查中最棘手的问题，它明确指向有**代理/网关**的存在。虽然最终我们没有完全确认您环境中502的来源，但它通常指向 **Nginx** 配置错误或上游服务（如我们的`app`容器）无法正常响应。

### **🔥 文件上传功能完整解决方案**

#### **问题现象**
- 文件上传成功，但访问文件URL时返回 `404 Not Found`
- 后端日志显示文件已保存，但前端无法显示图片

#### **根本原因分析**
1. **路径不一致**: 后端配置的文件存储路径与nginx静态文件服务路径不匹配
2. **nginx配置错误**: nginx.conf中的 `location /uploads/` 配置的alias路径错误
3. **容器挂载问题**: docker-compose.yml中的volume挂载路径配置不当

#### **完整解决步骤**

**第一步：修改后端配置文件**
```bash
# 编辑后端配置文件
sudo nano /opt/smartadmin/backend/sa-base/src/main/resources/prod/sa-base.yaml

# 修改文件存储配置
file-storage:
  local:
    upload-path: /usr/share/nginx/html/uploads/  # 与nginx容器内路径一致
    url-prefix: /uploads/
```

**第二步：修改docker-compose.yml环境变量**
```yaml
services:
  app:
    environment:
      # 🔥 关键：通过环境变量覆盖配置文件设置
      - FILE_STORAGE_LOCAL_UPLOAD_PATH=/usr/share/nginx/html/uploads/
      - FILE_STORAGE_LOCAL_URL_PREFIX=/uploads/
    volumes:
      # 🔥 关键：挂载前端目录到容器内，让后端可以写入文件
      - ./frontend:/usr/share/nginx/html
```

**第三步：修正nginx配置文件**
```bash
# 编辑nginx配置
sudo nano /opt/smartadmin/nginx/nginx.conf

# 在server块中添加uploads静态文件服务配置
server {
    listen 80;
    server_name localhost;
    charset utf-8;

    # 处理前端页面请求
    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # 🔥 关键：处理文件上传的静态文件访问
    location /uploads/ {
        alias /usr/share/nginx/html/uploads/;  # 确保路径正确
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 处理后端API请求
    location /api/ {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass http://backend_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

**第四步：重新构建和启动服务**
```bash
# 进入项目目录
cd /opt/smartadmin

# 停止现有服务
sudo docker compose down

# 重新构建并启动
sudo docker compose up -d --build

# 检查服务状态
sudo docker compose ps

# 查看后端日志确认配置生效
sudo docker compose logs app | grep -i "file-storage"
```

**第五步：验证配置**
```bash
# 进入nginx容器检查目录结构
sudo docker exec -it smartadmin-nginx ls -la /usr/share/nginx/html/uploads/

# 进入app容器检查环境变量
sudo docker exec -it smartadmin-app env | grep FILE_STORAGE

# 测试nginx配置语法
sudo docker exec -it smartadmin-nginx nginx -t

# 重新加载nginx配置（如果只修改了nginx.conf）
sudo docker exec -it smartadmin-nginx nginx -s reload
```

#### **文件存储位置说明**
- **主机存储位置**: `/opt/smartadmin/frontend/uploads/`
- **容器内存储位置**: `/usr/share/nginx/html/uploads/`
- **访问URL格式**: `http://您的服务器IP/uploads/文件路径`
- **实际文件路径示例**: `/opt/smartadmin/frontend/uploads/public/common/filename.jpg`
- **对应访问URL**: `http://您的服务器IP/uploads/public/common/filename.jpg`

#### **关键成功要素**
1. **路径一致性**: 确保环境变量、volume挂载、nginx alias三者路径完全一致
2. **环境变量优先级**: Spring Boot中环境变量会覆盖配置文件设置
3. **nginx静态文件服务**: 必须正确配置 `location /uploads/` 的alias路径
4. **容器间共享**: 通过volume挂载让app和nginx容器共享同一个文件目录

#### **常用排错命令**
```bash
# 查看文件是否真实存在
ls -la /opt/smartadmin/frontend/uploads/

# 检查nginx容器内文件
sudo docker exec -it smartadmin-nginx ls -la /usr/share/nginx/html/uploads/

# 查看nginx访问日志
sudo docker compose logs nginx | grep uploads

# 查看后端文件上传日志
sudo docker compose logs app | grep -i upload

# 测试文件访问权限
sudo docker exec -it smartadmin-nginx cat /usr/share/nginx/html/uploads/public/common/某个文件名
```

---

## **附录：支持前端目录文件存储的 docker-compose.yml 配置**

如果你选择将文件存储在前端目录中（`/opt/smartadmin/frontend/uploads`），使用以下配置：

```yaml
version: '3.8'

services:
  # 后端Java应用服务
  app:
    build: ./backend
    container_name: smartadmin-app
    restart: always
    depends_on:
      - redis
    environment:
      # 数据库配置（请替换为你的实际配置）
      - SPRING_DATASOURCE_URL=*****************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=your_db_username
      - SPRING_DATASOURCE_PASSWORD=your_db_password
      - SPRING_DATA_REDIS_HOST=redis
      - SPRING_DATA_REDIS_PORT=6379
      # 🔥 关键：文件上传配置使用前端目录
      - FILE_STORAGE_LOCAL_UPLOAD_PATH=/usr/share/nginx/html/uploads/
      - FILE_STORAGE_LOCAL_URL_PREFIX=/uploads/
    volumes:
      # 🔥 关键：挂载前端目录到容器内，让后端可以写入文件
      - ./frontend:/usr/share/nginx/html
    networks:
      - smartadmin-net

  # Redis服务
  redis:
    image: redis:6-alpine
    container_name: smartadmin-redis
    restart: always
    volumes:
      - redis_data:/data
    networks:
      - smartadmin-net

  # Nginx反向代理服务
  nginx:
    image: nginx:1.25-alpine
    container_name: smartadmin-nginx
    restart: always
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./frontend:/usr/share/nginx/html  # 前端文件（包含uploads目录）
    depends_on:
      - app
    networks:
      - smartadmin-net

networks:
  smartadmin-net:
    driver: bridge

volumes:
  redis_data:
```

**关键配置说明：**
1. **环境变量**：通过 `FILE_STORAGE_LOCAL_UPLOAD_PATH` 和 `FILE_STORAGE_LOCAL_URL_PREFIX` 覆盖配置文件中的设置
2. **Volume挂载**：将 `./frontend` 目录挂载到容器内的 `/usr/share/nginx/html`，这样后端和nginx都能访问同一个目录
3. **路径统一**：后端写入 `/usr/share/nginx/html/uploads/`，nginx 直接提供 `/uploads/` 路径的静态文件服务

**优势：**
- 配置简单，不需要额外的nginx location配置
- 文件直接存储在前端静态资源目录中
- nginx 自动提供静态文件服务
- 容器间共享同一个文件存储目录
