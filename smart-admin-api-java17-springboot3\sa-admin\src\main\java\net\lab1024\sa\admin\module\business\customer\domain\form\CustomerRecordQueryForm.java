package net.lab1024.sa.admin.module.business.customer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.base.common.domain.PageParam;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

/**
 * 客户跟进记录查询表单
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:00:00
 * @Copyright 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CustomerRecordQueryForm extends PageParam {

    @Schema(description = "客户唯一编码")
    @Length(max = 255, message = "客户唯一编码最多255字符")
    private String customerUniqueCode;

    @Schema(description = "跟进类型")
    private String type;

    @Schema(description = "跟进状态")
    private String status;

    @Schema(description = "跟进时间开始")
    private LocalDateTime followTimeStart;

    @Schema(description = "跟进时间结束")
    private LocalDateTime followTimeEnd;

    @Schema(description = "删除状态")
    private Boolean deletedFlag;
} 