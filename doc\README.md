# 📚 SmartAdmin 项目文档中心

欢迎来到 SmartAdmin 项目文档中心！本文档库包含了项目开发、部署、运维的完整指南。

## 🗂️ 文档目录结构

### 📖 [01-项目介绍](./01-项目介绍/)
- [项目概述](./01-项目介绍/项目概述.md) - 项目基本信息和技术架构
- [功能特性](./01-项目介绍/功能特性.md) - 核心功能和亮点介绍

### 📋 [02-开发规范](./02-开发规范/)
- [前端开发规范](./02-开发规范/前端开发规范.md) - 页面布局和组件开发标准
- [数据库设计规范](./02-开发规范/数据库设计规范.md) - 数据库结构和命名规范

### 🚀 [03-部署运维](./03-部署运维/)
- [Docker部署指南](./03-部署运维/Docker部署指南.md) - 容器化部署完整流程
- [传统部署指南](./03-部署运维/传统部署指南.md) - 传统服务器部署方式
- [运维操作手册](./03-部署运维/运维操作手册.md) - 日常运维命令和监控

### 🔧 [04-功能模块](./04-功能模块/)
- [客户管理模块](./04-功能模块/客户管理模块.md) - 客户视角功能详细说明
- [链接视角模块](./04-功能模块/链接视角模块.md) - 链接分析功能和数据库结构

### 🔒 [05-安全加固](./05-安全加固/)
- [员工密码重置安全加固](./05-安全加固/员工密码重置安全加固.md) - 密码重置功能安全增强
- [超级管理员账号保护机制](./05-安全加固/超级管理员账号保护机制.md) - 超管账号完整保护方案
- [高级安全验证功能技术分析](./05-安全加固/高级安全验证功能技术分析.md) - 邮件验证双重安全机制分析

### 💡 [05-开发笔记](./05-开发笔记/)
- [配置管理](./05-开发笔记/配置管理.md) - 项目配置相关经验和注意事项
- [开发经验](./05-开发笔记/开发经验.md) - 开发过程中的技术总结和最佳实践
- [问题解决](./05-开发笔记/问题解决.md) - 常见问题和解决方案

### 📅 [06-项目历史](./06-项目历史/)
- [更新日志](./06-项目历史/更新日志.md) - 详细的项目变更历史记录
- [项目里程碑](./06-项目历史/项目里程碑.md) - 项目发展历程和重要节点
## 🔍 快速导航

### 新手入门
1. 📖 [项目概述](./01-项目介绍/项目概述.md) - 了解项目基本情况
2. 🚀 [Docker部署指南](./03-部署运维/Docker部署指南.md) - 快速部署项目
3. 📋 [前端开发规范](./02-开发规范/前端开发规范.md) - 学习开发规范

### 开发人员
1. 💡 [开发经验](./05-开发笔记/开发经验.md) - 查看开发经验总结
2. 💡 [配置管理](./05-开发笔记/配置管理.md) - 了解配置相关注意事项
3. 🔧 [功能模块文档](./04-功能模块/) - 查看具体功能实现
4. 🔒 [安全加固文档](./05-安全加固/) - 了解系统安全机制

### 运维人员
1. 🚀 [Docker部署指南](./03-部署运维/Docker部署指南.md) - 容器化部署
2. 🚀 [运维操作手册](./03-部署运维/运维操作手册.md) - 日常运维命令
3. 💡 [问题解决](./05-开发笔记/问题解决.md) - 常见问题排查

## 📝 文档维护

### 更新规范
- 每次功能更新后，及时更新相关文档
- 重要变更需要在 [更新日志](./06-项目历史/更新日志.md) 中记录
- 新增功能需要补充对应的功能模块文档

### 贡献指南
- 文档使用 Markdown 格式编写
- 保持文档结构清晰，使用合适的标题层级
- 添加必要的代码示例和截图说明
- 及时更新文档间的交叉引用链接

## 🏷️ 版本信息

- **文档版本**: v2.1
- **最后更新**: 2024-08-01
- **维护人员**: 开发团队
- **最新更新**: 新增超级管理员账号保护机制文档

---

💡 **提示**: 如果您在使用过程中发现文档有误或需要补充，请及时反馈给开发团队。
