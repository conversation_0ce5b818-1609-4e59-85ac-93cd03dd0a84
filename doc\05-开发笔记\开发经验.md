# SmartAdmin 开发经验总结

## 📋 概述

本文档汇总了 SmartAdmin 项目开发过程中的重要经验、最佳实践和问题解决方案，为开发团队提供参考。

## 🎯 前端开发经验

### Vue3 组件开发

#### 表单组件改造经验
以任务表单（task-form.vue）为例，总结了组件改造的完整流程：

**需求背景**：
- 接收者字段：从输入框改为下拉选择
- 发布者字段：自动填充当前用户，不可编辑

**实现步骤**：

1. **组件替换**
```vue
<!-- 原始输入框 -->
<a-input v-model:value="form.receiver" placeholder="请输入接收者" />

<!-- 改为下拉选择 -->
<a-select
  v-model:value="form.receiver"
  placeholder="请选择接收者"
  show-search
  :filter-option="filterOption"
  style="width: 100%"
>
  <a-select-option v-for="option in receiverOptions" :key="option.value" :value="option.value">
    {{ option.label }}
  </a-select-option>
</a-select>
```

2. **异步数据加载**
```javascript
import { employeeApi } from '/@/api/system/employee-api';

const receiverOptions = ref([]);

async function loadReceiverOptions() {
  try {
    const res = await employeeApi.queryAll();
    if (res.data) {
      receiverOptions.value = res.data.map(employee => ({
        value: employee.actualName,
        label: employee.actualName,
      }));
    }
  } catch (err) {
    console.error("Failed to load receivers:", err);
    smartSentry.captureError(err);
  }
}
```

3. **搜索功能实现**
```javascript
const filterOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};
```

#### 用户状态管理
**自动填充当前用户信息**：
```javascript
import { useUserStore } from '/@/store/modules/user-store';

const userStore = useUserStore();

// 在表单初始化时自动填充
function initForm() {
  form.value.publisher = userStore.userInfo.actualName;
}
```

### API 调用最佳实践

#### 错误处理机制
```javascript
try {
  const res = await api.someMethod();
  if (res.data) {
    // 处理成功响应
  }
} catch (err) {
  console.error("API调用失败:", err);
  smartSentry.captureError(err); // 错误上报
  message.error('操作失败，请重试');
}
```

#### API 方法确认
- 开发前先检查 API 文件确认正确的方法名
- 常见错误：`queryListAll` vs `queryAll`
- 建议：使用 IDE 的自动补全功能避免拼写错误

## 🗄️ 数据库开发经验

### 多数据源查询优化

#### 字符集兼容性问题
**问题**：不同数据库的字符集排序规则不一致导致 JOIN 失败
```sql
-- 错误示例
SELECT * FROM smart_admin_v3.table1 t1
JOIN lirun.table2 t2 ON t1.code = t2.code;
```

**解决方案**：使用 CONVERT 和 COLLATE 函数统一字符集
```sql
SELECT * FROM smart_admin_v3.table1 t1
JOIN lirun.table2 t2 ON CONVERT(t1.code USING utf8mb4) COLLATE utf8mb4_general_ci = 
                        CONVERT(t2.code USING utf8mb4) COLLATE utf8mb4_general_ci;
```

#### 实时计算 vs 预计算权衡

**场景**：客户分类计算
- **预计算方案**：定时同步数据到汇总表
  - 优点：查询速度快
  - 缺点：数据可能不准确，同步复杂

- **实时计算方案**：直接查询源数据
  - 优点：数据准确性高
  - 缺点：查询复杂度高

**最佳实践**：根据业务需求选择
- 对准确性要求高的场景：选择实时计算
- 对性能要求高的场景：选择预计算 + 定时同步

### SQL 优化经验

#### CTE（公共表表达式）的使用
```sql
-- 使用 CTE 提高 SQL 可读性
WITH FilteredOrders AS (
    SELECT * FROM lirun.订单明细 
    WHERE 付款时间 BETWEEN #{startDate} AND #{endDate}
),
CustomerClassification AS (
    SELECT 
        客户唯一编码,
        CASE 
            WHEN EXISTS (历史订单查询) THEN '老客'
            ELSE '新客'
        END as 客户类型
    FROM FilteredOrders
)
SELECT * FROM CustomerClassification;
```

#### 索引优化策略
- **组合索引**：遵循最左前缀原则
- **覆盖索引**：减少回表查询
- **条件索引**：针对特定条件创建索引

## 🔧 系统架构经验

### 配置管理
**双重配置文件问题**：
- SmartAdmin 项目需要同时维护两个位置的配置文件
- 位置1：`smart-admin-api-java17-springboot3/sa-base/src/main/resources/dev/sa-base.yaml`
- 位置2：`sa-base/src/main/resources/dev/sa-base.yaml`

**解决方案**：
- 建立配置同步检查清单
- 使用脚本自动同步配置文件
- 在部署流程中增加配置一致性检查

### 权限控制设计
**菜单权限配置**：
```sql
-- 标准菜单权限配置
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, path, component, perms) VALUES
(306, '客户管理', 1, 0, NULL, NULL, NULL),
(307, '客户视角', 2, 306, '/business/customer/view', '/business/customer/customer-view', 'customer:view:query');
```

**权限验证**：
- 前端：路由守卫 + 按钮权限控制
- 后端：注解 + AOP 拦截

## 🚨 问题排查经验

### 常见错误类型

#### 1. API 调用错误
**错误信息**：`TypeError: employeeApi.queryListAll is not a function`
**排查步骤**：
1. 检查 API 文件中的实际方法名
2. 确认 import 语句正确
3. 验证 API 方法的参数和返回值

#### 2. 数据库连接错误
**错误信息**：`Access denied for user 'root'@'IP地址'`
**排查步骤**：
1. 检查数据库用户权限
2. 验证 IP 白名单设置
3. 确认密码正确性

#### 3. 字符集问题
**错误信息**：`Illegal mix of collations`
**排查步骤**：
1. 检查表的字符集设置
2. 使用 CONVERT 函数统一字符集
3. 修改表结构统一排序规则

### 调试技巧

#### 前端调试
- 使用 Vue DevTools 查看组件状态
- Console.log 输出关键变量
- Network 面板检查 API 请求

#### 后端调试
- 使用 P6Spy 监控 SQL 执行
- 日志级别调整为 DEBUG
- 使用断点调试复杂逻辑

#### 数据库调试
- 使用 EXPLAIN 分析查询计划
- 开启慢查询日志
- 使用 SHOW PROCESSLIST 查看运行状态

## 💡 开发最佳实践

### 代码规范
1. **命名规范**：使用有意义的变量和方法名
2. **注释规范**：关键逻辑必须有注释说明
3. **错误处理**：所有 API 调用都要有错误处理
4. **代码复用**：提取公共方法和组件

### 测试策略
1. **单元测试**：核心业务逻辑编写单元测试
2. **集成测试**：API 接口编写集成测试
3. **端到端测试**：关键业务流程编写 E2E 测试

### 性能优化
1. **前端优化**：
   - 组件懒加载
   - 图片懒加载
   - 合理使用缓存

2. **后端优化**：
   - 数据库连接池配置
   - 缓存策略设计
   - 异步处理长耗时任务

3. **数据库优化**：
   - 索引设计优化
   - 查询语句优化
   - 分库分表策略

## 📚 学习资源

### 官方文档
- [Vue3 官方文档](https://v3.vuejs.org/)
- [Ant Design Vue](https://antdv.com/)
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)

### 社区资源
- [SmartAdmin 官方社区](https://smartadmin.vip)
- [GitHub Issues](https://github.com/1024-lab/smart-admin)

---

💡 **提示**：开发经验需要在实践中不断积累和总结，建议团队定期进行技术分享和经验交流，共同提升开发效率和代码质量。
