package net.lab1024.sa.admin.module.business.customer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.base.common.domain.PageParam;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 客户视角查询表单
 *
 * <AUTHOR>
 * @Date 2025-01-09 15:00:00
 * @Copyright 1.0
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class CustomerViewQueryForm extends PageParam {

    @Schema(description = "客户唯一编码")
    @Length(max = 255, message = "客户唯一编码最多255字符")
    private String customerUniqueCode;

    @Schema(description = "最近订单号")
    @Length(max = 255, message = "订单号最多255字符")
    private String latestOrderNo;

    @Schema(description = "最近成交店铺")
    @Length(max = 255, message = "店铺名称最多255字符")
    private String latestShopName;

    @Schema(description = "流失风险：正常/已流失")
    private String churnRisk;

    @Schema(description = "会员等级：S/A/B/C")
    private String memberLevel;

    @Schema(description = "销售额最小值")
    private BigDecimal salesAmountMin;

    @Schema(description = "销售额最大值")
    private BigDecimal salesAmountMax;

    @Schema(description = "购买次数最小值")
    private Integer purchaseCountMin;

    @Schema(description = "购买次数最大值")
    private Integer purchaseCountMax;

    @Schema(description = "首次购买日期开始")
    private LocalDate firstPurchaseDateBegin;

    @Schema(description = "首次购买日期结束")
    private LocalDate firstPurchaseDateEnd;

    @Schema(description = "最后购买日期开始")
    private LocalDate lastPurchaseDateBegin;

    @Schema(description = "最后购买日期结束")
    private LocalDate lastPurchaseDateEnd;

    @Schema(description = "删除状态")
    private Boolean deletedFlag;
}
