package net.lab1024.sa.admin.module.business.customer.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.Map;
import java.util.List;

import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerLinkQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkAnalysisVO;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkDetailVO;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkOrderDetailVO;
import net.lab1024.sa.admin.module.business.customer.service.CustomerLinkService;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * 链接视角 Controller
 *
 * <AUTHOR>
 * @Date 2025-01-17 10:00:00
 * @Copyright 1.0
 */

@RestController
@RequestMapping("/api")
@Tag(name = "链接视角")
@OperateLog
public class CustomerLinkController {

    @Resource
    private CustomerLinkService customerLinkService;

    @Operation(summary = "分页查询链接数据 <AUTHOR>
    @PostMapping("/customer/link/queryPage")
    @SaCheckPermission("customer:link:query")
    public ResponseDTO<PageResult<Object>> queryPage(@RequestBody @Valid Map<String, Object> queryForm) {
        // TODO: 实现分页查询逻辑
        return ResponseDTO.ok(new PageResult<>());
    }

    @Operation(summary = "查看链接详情 <AUTHOR>
    @GetMapping("/customer/link/detail/{customerCode}")
    @SaCheckPermission("customer:link:detail")
    public ResponseDTO<Object> getDetail(@PathVariable String customerCode) {
        // TODO: 实现查看详情逻辑
        return ResponseDTO.ok();
    }

    @Operation(summary = "管理链接关系 <AUTHOR>
    @PostMapping("/customer/link/manage")
    @SaCheckPermission("customer:link:manage")
    public ResponseDTO<String> manageRelation(@RequestBody @Valid Map<String, Object> param) {
        // TODO: 实现管理链接关系逻辑
        return ResponseDTO.ok("链接关系管理成功");
    }

    @Operation(summary = "获取链接统计 <AUTHOR>
    @GetMapping("/customer/link/statistics/{customerCode}")
    @SaCheckPermission("customer:link:query")
    public ResponseDTO<Object> getStatistics(@PathVariable String customerCode) {
        // TODO: 实现获取统计信息逻辑
        return ResponseDTO.ok();
    }

    @Operation(summary = "导出链接数据 <AUTHOR>
    @PostMapping("/api/customer/link/exportExcel")
    @SaCheckPermission("customer:link:export")
    public void exportExcel(@RequestBody @Valid Map<String, Object> param) {
        // TODO: 实现导出功能
    }

    @Operation(summary = "获取客户关联网络 <AUTHOR>
    @GetMapping("/api/customer/link/network/{customerCode}")
    @SaCheckPermission("customer:link:query")
    public ResponseDTO<Object> getNetworkData(@PathVariable String customerCode) {
        // TODO: 实现获取网络数据逻辑
        return ResponseDTO.ok();
    }

    @PostMapping("/customer/link/analysis")
    @Operation(summary = "查询链接分析")
    // @SaCheckPermission("customer:link:analysis") // 临时注释权限验证，用于调试
    public ResponseDTO<CustomerLinkAnalysisVO> getAnalysis(@RequestBody CustomerLinkQueryForm queryForm) {
        try {
            CustomerLinkAnalysisVO analysis = customerLinkService.getAnalysis(queryForm);
            return ResponseDTO.ok(analysis);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseDTO.userErrorParam("查询分析数据失败: " + e.getMessage());
        }
    }

    @PostMapping("/customer/link/detail")
    @Operation(summary = "查询复购明细")
    // @SaCheckPermission("customer:link:detail") // 临时注释权限验证，用于调试
    public ResponseDTO<List<CustomerLinkDetailVO>> getDetail(@RequestBody CustomerLinkQueryForm queryForm) {
        try {
            System.out.println("收到复购明细查询请求: " + queryForm);
            List<CustomerLinkDetailVO> details = customerLinkService.getDetail(queryForm);
            System.out.println("查询到复购明细数据条数: " + (details != null ? details.size() : 0));
            if (details != null && !details.isEmpty()) {
                System.out.println("第一条数据: " + details.get(0));
            }
            return ResponseDTO.ok(details);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseDTO.userErrorParam("查询复购明细失败: " + e.getMessage());
        }
    }

    @Operation(summary = "下载复购明细订单数据 <AUTHOR>
    @PostMapping("/customer/link/downloadDetail")
    @SaCheckPermission("customer:link:query")
    public void downloadDetail(@RequestBody CustomerLinkQueryForm queryForm, HttpServletResponse response) {
        try {
            System.out.println("=== 开始下载订单明细 ===");
            System.out.println("收到下载订单明细请求: " + queryForm);

            List<CustomerLinkOrderDetailVO> orderDetails = customerLinkService.downloadOrderDetail(queryForm);
            System.out.println("查询到订单明细数据条数: " + (orderDetails != null ? orderDetails.size() : 0));

            // 验证数据不为空
            if (orderDetails == null || orderDetails.isEmpty()) {
                System.out.println("警告：订单明细数据为空！");
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                response.getWriter().write("{\"code\":400,\"msg\":\"没有找到符合条件的订单数据\"}");
                return;
            }

            // 打印前几条数据用于调试
            System.out.println("前3条数据样例：");
            for (int i = 0; i < Math.min(3, orderDetails.size()); i++) {
                CustomerLinkOrderDetailVO detail = orderDetails.get(i);
                System.out.println("第" + (i+1) + "条: " + detail.toString());
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");

            // 生成文件名
            String fileName = "复购明细订单数据_" + System.currentTimeMillis() + ".xlsx";
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            System.out.println("设置文件名: " + fileName);

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("复购明细订单数据");
            System.out.println("创建Excel工作簿和工作表成功");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "客户唯一编码", "原始单号", "平台货品ID", "商家编码", "数量",
                "已付金额", "付款时间", "店铺名称", "购买类型", "客户总购买次数", "当日购买次序"
            };

            System.out.println("开始创建表头，共" + headers.length + "列");
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            System.out.println("表头创建完成");

            // 填充数据
            System.out.println("开始填充数据，共" + orderDetails.size() + "行");
            for (int i = 0; i < orderDetails.size(); i++) {
                Row row = sheet.createRow(i + 1);
                CustomerLinkOrderDetailVO detail = orderDetails.get(i);

                // 添加空值检查和详细日志
                if (detail == null) {
                    System.out.println("警告：第" + (i+1) + "行数据为null，跳过");
                    continue;
                }

                // 逐个字段填充，并添加空值保护
                row.createCell(0).setCellValue(detail.getCustomerUniqueCode() != null ? detail.getCustomerUniqueCode() : "");
                row.createCell(1).setCellValue(detail.getOriginalOrderNo() != null ? detail.getOriginalOrderNo() : "");
                row.createCell(2).setCellValue(detail.getPlatformGoodsId() != null ? detail.getPlatformGoodsId() : "");
                row.createCell(3).setCellValue(detail.getMerchantCode() != null ? detail.getMerchantCode() : "");
                row.createCell(4).setCellValue(detail.getQuantity() != null ? detail.getQuantity() : 0);
                row.createCell(5).setCellValue(detail.getPaidAmount() != null ? detail.getPaidAmount().doubleValue() : 0.0);
                row.createCell(6).setCellValue(detail.getPaymentTime() != null ? detail.getPaymentTime().toString() : "");
                row.createCell(7).setCellValue(detail.getShopName() != null ? detail.getShopName() : "");
                row.createCell(8).setCellValue(detail.getRepurchaseTimes() != null ? detail.getRepurchaseTimes() : "");
                row.createCell(9).setCellValue(detail.getPurchaseOrder() != null ? detail.getPurchaseOrder() : 0);
                row.createCell(10).setCellValue(detail.getRepurchaseCycleDays() != null ? detail.getRepurchaseCycleDays() : 0);

                // 每100行打印一次进度
                if ((i + 1) % 100 == 0) {
                    System.out.println("已处理 " + (i + 1) + " 行数据");
                }
            }
            System.out.println("数据填充完成，共处理 " + orderDetails.size() + " 行");

            // 自动调整列宽
            System.out.println("开始调整列宽");
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            System.out.println("列宽调整完成");

            // 写入响应流
            System.out.println("开始写入响应流");
            workbook.write(response.getOutputStream());
            workbook.close();
            System.out.println("=== 下载完成 ===");

        } catch (Exception e) {
            System.err.println("下载过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"下载失败: " + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
    }
}