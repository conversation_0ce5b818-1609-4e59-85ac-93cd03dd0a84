<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.customer.dao.CustomerViewDao">

    <!-- 基础字段 -->
    <sql id="base_columns">
        crm.客户唯一编码,
        crm.总销售额,
        crm.总成本,
        crm.总利润,
        crm.总客户毛利率,
        crm.购买次数,
        crm.复购周期,
        crm.首次成交日期,
        crm.上次购买日期,
        crm.流失风险,
        crm.上次购买后距今天数,
        crm.购买天数,
        crm.最近一次下单店铺,
        crm.最近下单原始单号
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerViewVO">
        SELECT
        <include refid="base_columns"/>
        FROM `crm_客户查询` crm
        <where>
            1=1
            <!-- 客户唯一编码 -->
            <if test="queryForm.customerUniqueCode != null and queryForm.customerUniqueCode != ''">
                AND INSTR(crm.客户唯一编码, #{queryForm.customerUniqueCode}) > 0
            </if>
            <!-- 最近订单号 -->
            <if test="queryForm.latestOrderNo != null and queryForm.latestOrderNo != ''">
                AND INSTR(crm.最近下单原始单号, #{queryForm.latestOrderNo}) > 0
            </if>
            <!-- 最近成交店铺 -->
            <if test="queryForm.latestShopName != null and queryForm.latestShopName != ''">
                AND INSTR(crm.最近一次下单店铺, #{queryForm.latestShopName}) > 0
            </if>
            <!-- 流失风险 -->
            <if test="queryForm.churnRisk != null and queryForm.churnRisk != ''">
                AND crm.流失风险 = #{queryForm.churnRisk}
            </if>
            <!-- 销售额范围 -->
            <if test="queryForm.salesAmountMin != null">
                AND crm.总销售额 >= #{queryForm.salesAmountMin}
            </if>
            <if test="queryForm.salesAmountMax != null">
                AND crm.总销售额 &lt;= #{queryForm.salesAmountMax}
            </if>
            <!-- 购买次数范围 -->
            <if test="queryForm.purchaseCountMin != null">
                AND crm.购买次数 >= #{queryForm.purchaseCountMin}
            </if>
            <if test="queryForm.purchaseCountMax != null">
                AND crm.购买次数 &lt;= #{queryForm.purchaseCountMax}
            </if>
            <!-- 首次购买日期范围 -->
            <if test="queryForm.firstPurchaseDateBegin != null">
                AND crm.首次成交日期 >= #{queryForm.firstPurchaseDateBegin}
            </if>
            <if test="queryForm.firstPurchaseDateEnd != null">
                AND crm.首次成交日期 &lt;= #{queryForm.firstPurchaseDateEnd}
            </if>
            <!-- 最后购买日期范围 -->
            <if test="queryForm.lastPurchaseDateBegin != null">
                AND crm.上次购买日期 >= #{queryForm.lastPurchaseDateBegin}
            </if>
            <if test="queryForm.lastPurchaseDateEnd != null">
                AND crm.上次购买日期 &lt;= #{queryForm.lastPurchaseDateEnd}
            </if>
        </where>
        ORDER BY crm.上次购买日期 DESC
    </select>

    <!-- 查询详情 -->
    <select id="getDetail" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerViewVO">
        SELECT
        <include refid="base_columns"/>
        FROM `crm_客户查询` crm
        WHERE crm.客户唯一编码 = #{customerId}
    </select>

    <!-- 获取统计信息 -->
    <select id="getStatistics" resultType="map">
        SELECT
            COUNT(*) as totalCount,
            COUNT(CASE WHEN 流失风险 = '正常' THEN 1 END) as normalCount,
            COUNT(CASE WHEN 流失风险 = '新客户' THEN 1 END) as newCount,
            COUNT(CASE WHEN 流失风险 = '低风险' THEN 1 END) as lowRiskCount,
            COUNT(CASE WHEN 流失风险 = '中风险' THEN 1 END) as mediumRiskCount,
            COUNT(CASE WHEN 流失风险 = '高风险' THEN 1 END) as highRiskCount,
            COUNT(CASE WHEN 流失风险 = '已流失' THEN 1 END) as churnedCount,
            COALESCE(SUM(总销售额), 0) as totalSalesAmount,
            COALESCE(AVG(购买次数), 0) as avgPurchaseCount,
            COALESCE(AVG(复购周期), 0) as avgRepurchaseCycle
        FROM `crm_客户查询`
    </select>

    <!-- 导出查询 -->
    <select id="exportQuery" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerViewVO">
        SELECT
        <include refid="base_columns"/>
        FROM `crm_客户查询` crm
        <where>
            1=1
            <!-- 客户唯一编码 -->
            <if test="queryForm.customerUniqueCode != null and queryForm.customerUniqueCode != ''">
                AND INSTR(crm.客户唯一编码, #{queryForm.customerUniqueCode}) > 0
            </if>
            <!-- 最近订单号 -->
            <if test="queryForm.latestOrderNo != null and queryForm.latestOrderNo != ''">
                AND INSTR(crm.最近下单原始单号, #{queryForm.latestOrderNo}) > 0
            </if>
            <!-- 最近成交店铺 -->
            <if test="queryForm.latestShopName != null and queryForm.latestShopName != ''">
                AND INSTR(crm.最近一次下单店铺, #{queryForm.latestShopName}) > 0
            </if>
            <!-- 流失风险 -->
            <if test="queryForm.churnRisk != null and queryForm.churnRisk != ''">
                AND crm.流失风险 = #{queryForm.churnRisk}
            </if>
            <!-- 销售额范围 -->
            <if test="queryForm.salesAmountMin != null">
                AND crm.总销售额 >= #{queryForm.salesAmountMin}
            </if>
            <if test="queryForm.salesAmountMax != null">
                AND crm.总销售额 &lt;= #{queryForm.salesAmountMax}
            </if>
            <!-- 购买次数范围 -->
            <if test="queryForm.purchaseCountMin != null">
                AND crm.购买次数 >= #{queryForm.purchaseCountMin}
            </if>
            <if test="queryForm.purchaseCountMax != null">
                AND crm.购买次数 &lt;= #{queryForm.purchaseCountMax}
            </if>
            <!-- 首次购买日期范围 -->
            <if test="queryForm.firstPurchaseDateBegin != null">
                AND crm.首次成交日期 >= #{queryForm.firstPurchaseDateBegin}
            </if>
            <if test="queryForm.firstPurchaseDateEnd != null">
                AND crm.首次成交日期 &lt;= #{queryForm.firstPurchaseDateEnd}
            </if>
            <!-- 最后购买日期范围 -->
            <if test="queryForm.lastPurchaseDateBegin != null">
                AND crm.上次购买日期 >= #{queryForm.lastPurchaseDateBegin}
            </if>
            <if test="queryForm.lastPurchaseDateEnd != null">
                AND crm.上次购买日期 &lt;= #{queryForm.lastPurchaseDateEnd}
            </if>
        </where>
        ORDER BY crm.上次购买日期 DESC
        LIMIT 5000
    </select>

</mapper>
