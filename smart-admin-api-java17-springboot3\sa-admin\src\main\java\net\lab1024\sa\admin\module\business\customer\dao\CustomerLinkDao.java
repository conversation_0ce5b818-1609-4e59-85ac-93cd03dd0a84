package net.lab1024.sa.admin.module.business.customer.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lab1024.sa.admin.module.business.customer.domain.entity.CustomerLinkEntity;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerLinkQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkAnalysisVO;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkDetailVO;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkOrderDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * 链接视角 DAO
 *
 * <AUTHOR>
 * @Date 2025-01-18 10:20:00
 * @Copyright 1.0
 */
@Mapper
@Component
public interface CustomerLinkDao extends BaseMapper<CustomerLinkEntity> {

    /**
     * 查询链接分析
     *
     * @param queryForm
     * @return
     */
    CustomerLinkAnalysisVO queryAnalysis(@Param("queryForm") CustomerLinkQueryForm queryForm);

    /**
     * 查询复购明细
     *
     * @param queryForm
     * @return
     */
    List<CustomerLinkDetailVO> queryDetail(@Param("queryForm") CustomerLinkQueryForm queryForm);

    /**
     * 下载复购明细的订单明细数据
     *
     * @param queryForm
     * @return
     */
    List<CustomerLinkOrderDetailVO> downloadOrderDetail(@Param("queryForm") CustomerLinkQueryForm queryForm);

}