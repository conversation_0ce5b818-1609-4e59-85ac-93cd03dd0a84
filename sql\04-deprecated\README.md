# 🗑️ 已废弃的 SQL 脚本

## 📋 概述

本目录包含已经废弃不再使用的 SQL 脚本，保留用于历史参考和问题排查。

## 📁 废弃脚本列表

### customer_classification_enhancement.sql
**废弃时间**: 2025-07-30  
**废弃原因**: 架构方案变更  

**原用途**:
- 为客户分类功能提供预计算增强方案
- 在 `crm_客户查询` 表中添加客户分类字段
- 通过存储过程批量更新客户分类数据

**废弃原因**:
1. **数据同步问题**: 预计算方案存在数据滞后问题，64.5% 的客户数据缺失
2. **架构变更**: 改为直接从 `lirun` 数据库实时计算，无需预计算表
3. **性能优化**: 实时计算方案性能更好，数据更准确

**替代方案**:
- 使用 `CustomerLinkMapper.xml` 中的实时计算逻辑
- 直接从 `lirun.订单明细` 表计算客户分类
- 移除对 `smart_admin_v3.crm_客户查询` 表的依赖

**相关表结构**:
```sql
-- 以下表结构已不再需要
ALTER TABLE smart_admin_v3.crm_客户查询 
ADD COLUMN 客户类型 ENUM('新客', '老客') DEFAULT NULL;

DROP TABLE IF EXISTS smart_admin_v3.t_customer_classification_log;
```

**影响范围**:
- 链接视角页面查询逻辑
- 客户分类统计功能
- 相关的定时任务和同步服务

## ⚠️ 重要提醒

### 不要执行废弃脚本
- 本目录中的脚本已经过时，请勿在生产环境执行
- 如需了解历史实现方案，可以查看脚本内容作为参考

### 清理建议
如果您的数据库中已经执行过这些废弃脚本，建议进行以下清理：

```sql
-- 清理客户分类相关字段（可选）
ALTER TABLE smart_admin_v3.crm_客户查询 
DROP COLUMN IF EXISTS 客户类型,
DROP COLUMN IF EXISTS 分类更新时间,
DROP COLUMN IF EXISTS 分类基准日期;

-- 删除客户分类日志表（可选）
DROP TABLE IF EXISTS smart_admin_v3.t_customer_classification_log;

-- 删除相关存储过程（可选）
DROP PROCEDURE IF EXISTS smart_admin_v3.UpdateCustomerClassification;
```

### 数据迁移
如果您需要从旧方案迁移到新方案：
1. 确认新的实时计算逻辑已部署
2. 测试链接视角页面功能正常
3. 备份相关数据后执行清理脚本

## 📚 相关文档

- [链接视角模块文档](../../doc/04-功能模块/链接视角模块.md) - 查看当前实现方案
- [数据库设计规范](../../doc/02-开发规范/数据库设计规范.md) - 了解数据库设计标准

---

💡 **历史记录**: 这些脚本记录了项目的技术演进过程，体现了从预计算到实时计算的架构优化历程。
