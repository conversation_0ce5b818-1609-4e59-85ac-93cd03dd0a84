package net.lab1024.sa.admin.module.business.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 链接视角 - 核心统计指标VO
 *
 * <AUTHOR>
 * @Date 2025-01-18 10:05:00
 * @Copyright 1.0
 */
@Data
public class CustomerLinkStatisticsVO {

    @Schema(description = "平台货品ID")
    private String platformGoodsId;

    @Schema(description = "订单数量")
    private Long orderCount;

    @Schema(description = "付款人数")
    private Long customerCount;

    @Schema(description = "复购人数")
    private Long repurchaseCustomerCount;

    @Schema(description = "复购率")
    private BigDecimal repurchaseRate;

    @Schema(description = "平均复购周期 (天)")
    private BigDecimal avgRepurchaseCycleDays;

    @Schema(description = "最小复购间隔 (天)")
    private Integer minRepurchaseIntervalDays;

    @Schema(description = "最大复购间隔 (天)")
    private Integer maxRepurchaseIntervalDays;

    @Schema(description = "平均复购间隔 (天)")
    private BigDecimal avgRepurchaseIntervalDays;
} 