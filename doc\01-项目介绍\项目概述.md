# SmartAdmin 项目概述

## 📋 项目简介

**SmartAdmin** 是一个基于现代技术栈的企业级快速开发平台，专注于提供高质量、安全、高效的解决方案。

### 核心特色
- 🔒 **安全优先**: 国内首个满足《网络安全-三级等保》、《数据安全》功能要求
- 🎯 **高质量代码**: 以「高质量代码」为核心，「简洁、高效、安全」为理念
- 🔧 **双版本支持**: 前端提供 JavaScript 和 TypeScript 双版本，后端提供 Java8+SpringBoot2.X 和 Java17+SpringBoot3.X 双版本
- 📚 **规范完善**: 开源六年来千余家企业验证的代码规范

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Vue3 + Vite5 + Pinia
- **UI组件**: Ant Design Vue 4.X
- **语言**: JavaScript/TypeScript 双版本支持
- **移动端**: UniApp (Vue3版本) + Uni-UI
- **支持平台**: APP、小程序、H5

### 后端技术栈
- **框架**: SpringBoot2/3 + Sa-Token
- **数据访问**: MyBatis-Plus
- **数据库**: 支持多种数据库
- **语言**: Java8/17 双版本支持

### 系统架构特点
- **四层架构**: Controller → Service → Manager → DAO
- **模块化设计**: 清晰的代码分包结构
- **多环境支持**: 开发、测试、预发布、生产环境配置
- **动态加载**: Smart-Reload 系统钩子，支持不重启更新

## 🔐 安全体系

### 网络安全
- 双因子登录认证
- 密码加密存储
- 密码复杂度要求
- 登录错误次数锁定
- 登录超时自动退出
- 数据脱敏处理

### 接口安全
- 请求参数加解密
- 返回内容加解密
- 支持国产加密算法
- 支持国外主流加密算法

## 🎯 核心功能

### 系统管理
- 员工管理
- 部门管理
- 角色权限管理
- 菜单管理
- 系统参数配置
- 数据字典管理
- 单号生成器

### 业务功能
- 客户管理系统
- 链接视角分析
- 任务管理
- 文件管理
- 水印功能

### 监控日志
- 服务器心跳日志
- 用户登录日志
- 操作日志记录
- 设备信息追踪（IP、浏览器、操作系统）

### 数据管理
- 表格自定义列
- 数据变更记录（基于Git Diff）
- 在线文档系统
- 意见反馈机制
- 版本记录管理

### 代码生成
- 基于表配置的代码生成
- 在线预览生成代码
- 支持代码下载

## 🌟 开发理念

### 代码质量
- 推崇高质量代码，代码即利剑
- 经过上百家公司验证的代码规范
- 清晰的目录结构和命名规范
- 完善的错误处理和日志记录

### 团队协作
- 团队高度配合默契
- 互相帮助，减少加班
- 主动思考，保持学习
- 热爱代码，更热爱生活

### 技术追求
- 业内最佳的API命名和写法
- 清晰的Layout布局代码
- 正确的Router加载方式
- 独有的配置文件维护方案

## 🔗 相关链接

- **官方文档**: [https://smartadmin.vip](https://smartadmin.vip)
- **在线预览**: [https://preview.smartadmin.vip](https://preview.smartadmin.vip)
- **移动端预览**: [https://app.smartadmin.vip](https://app.smartadmin.vip/#/pages/login/login)
- **开源地址**: [1024创新实验室](https://www.1024lab.net/)

## 📊 项目数据

- **开源时间**: 6年+
- **企业用户**: 1000+ 家企业验证使用
- **技术支持**: 中国·洛阳 1024创新实验室
- **更新频率**: 持续更新维护

---

💡 **提示**: 本项目致力于为开发者提供一个高质量、安全、易用的企业级开发平台，让开发者能够专注于业务逻辑的实现，而不是基础架构的搭建。
