package net.lab1024.sa.admin.module.system.employee.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.system.employee.domain.form.*;
import net.lab1024.sa.admin.module.system.employee.domain.vo.EmployeeVO;
import net.lab1024.sa.admin.module.system.employee.service.EmployeeService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import net.lab1024.sa.base.module.support.apiencrypt.annotation.ApiDecrypt;
import net.lab1024.sa.base.module.support.securityprotect.service.Level3ProtectConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 员工
 *
 * <AUTHOR> 卓大
 * @Date 2021-12-09 22:57:49
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@RestController
@Tag(name = AdminSwaggerTagConst.System.SYSTEM_EMPLOYEE)
public class EmployeeController {

    @Resource
    private EmployeeService employeeService;

    @Resource
    private Level3ProtectConfigService level3ProtectConfigService;

    @PostMapping("/employee/query")
    @Operation(summary = "员工管理查询 <AUTHOR>
    public ResponseDTO<PageResult<EmployeeVO>> query(@Valid @RequestBody EmployeeQueryForm query) {
        return employeeService.queryEmployee(query);
    }

    @Operation(summary = "添加员工(返回添加员工的密码) <AUTHOR>
    @PostMapping("/employee/add")
    @SaCheckPermission("system:employee:add")
    public ResponseDTO<String> addEmployee(@Valid @RequestBody EmployeeAddForm employeeAddForm) {
        return employeeService.addEmployee(employeeAddForm);
    }

    @Operation(summary = "更新员工 <AUTHOR>
    @PostMapping("/employee/update")
    @SaCheckPermission("system:employee:update")
    public ResponseDTO<String> updateEmployee(@Valid @RequestBody EmployeeUpdateForm employeeUpdateForm) {
        return employeeService.updateEmployee(employeeUpdateForm);
    }

    @Operation(summary = "更新员工个人中心信息 <AUTHOR>
    @PostMapping("/employee/update/center")
    public ResponseDTO<String> updateCenter(@Valid @RequestBody EmployeeUpdateCenterForm updateCenterForm) {
        updateCenterForm.setEmployeeId(SmartRequestUtil.getRequestUserId());
        return employeeService.updateCenter(updateCenterForm);
    }

    @Operation(summary = "更新登录人头像 <AUTHOR>
    @PostMapping("/employee/update/avatar")
    public ResponseDTO<String> updateAvatar(@Valid @RequestBody EmployeeUpdateAvatarForm employeeUpdateAvatarForm) {
        employeeUpdateAvatarForm.setEmployeeId(SmartRequestUtil.getRequestUserId());
        return employeeService.updateAvatar(employeeUpdateAvatarForm);
    }

    @Operation(summary = "更新员工禁用/启用状态 <AUTHOR>
    @GetMapping("/employee/update/disabled/{employeeId}")
    @SaCheckPermission("system:employee:disabled")
    public ResponseDTO<String> updateDisableFlag(@PathVariable Long employeeId) {
        return employeeService.updateDisableFlag(employeeId);
    }

    @Operation(summary = "批量删除员工 <AUTHOR>
    @PostMapping("/employee/update/batch/delete")
    @SaCheckPermission("system:employee:delete")
    public ResponseDTO<String> batchUpdateDeleteFlag(@RequestBody List<Long> employeeIdList) {
        return employeeService.batchUpdateDeleteFlag(employeeIdList);
    }

    @Operation(summary = "批量调整员工部门 <AUTHOR>
    @PostMapping("/employee/update/batch/department")
    @SaCheckPermission("system:employee:department:update")
    public ResponseDTO<String> batchUpdateDepartment(@Valid @RequestBody EmployeeBatchUpdateDepartmentForm batchUpdateDepartmentForm) {
        return employeeService.batchUpdateDepartment(batchUpdateDepartmentForm);
    }

    @Operation(summary = "修改密码 <AUTHOR>
    @PostMapping("/employee/update/password")
    @ApiDecrypt
    public ResponseDTO<String> updatePassword(@Valid @RequestBody EmployeeUpdatePasswordForm updatePasswordForm) {
        updatePasswordForm.setEmployeeId(SmartRequestUtil.getRequestUserId());
        return employeeService.updatePassword(SmartRequestUtil.getRequestUser(), updatePasswordForm);
    }

    @Operation(summary = "获取密码复杂度 <AUTHOR>
    @GetMapping("/employee/getPasswordComplexityEnabled")
    @ApiDecrypt
    public ResponseDTO<Boolean> getPasswordComplexityEnabled() {
        return ResponseDTO.ok(level3ProtectConfigService.isPasswordComplexityEnabled());
    }

    @Operation(summary = "重置员工密码（旧接口，已废弃） <AUTHOR>
    @GetMapping("/employee/update/password/reset/{employeeId}")
    @SaCheckPermission("system:employee:password:reset")
    @Deprecated
    public ResponseDTO<String> resetPassword(@PathVariable Long employeeId) {
        return ResponseDTO.userErrorParam("此接口已废弃，请使用新的重置密码接口");
    }

    @Operation(summary = "重置员工密码（需要当前密码验证） <AUTHOR>
    @PostMapping("/employee/update/password/reset")
    @SaCheckPermission("system:employee:password:reset")
    @ApiDecrypt
    public ResponseDTO<String> resetPasswordWithAuth(@Valid @RequestBody EmployeeResetPasswordForm form) {
        RequestUser currentUser = SmartRequestUtil.getRequestUser();
        return employeeService.resetPasswordWithAuth(form, currentUser);
    }

    @Operation(summary = "查询员工-根据部门id <AUTHOR>
    @GetMapping("/employee/getAllEmployeeByDepartmentId/{departmentId}")
    public ResponseDTO<List<EmployeeVO>> getAllEmployeeByDepartmentId(@PathVariable Long departmentId) {
        return employeeService.getAllEmployeeByDepartmentId(departmentId);
    }

    @Operation(summary = "查询所有员工 <AUTHOR>
    @GetMapping("/employee/queryAll")
    public ResponseDTO<List<EmployeeVO>> queryAllEmployee(@RequestParam(value = "disabledFlag", required = false) Boolean disabledFlag) {
        return employeeService.queryAllEmployee(disabledFlag);
    }

}
