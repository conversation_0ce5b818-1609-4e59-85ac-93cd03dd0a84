# 链接视角页面数据库结构文档

## 📋 概述

本文档记录了链接视角页面的数据库结构、查询逻辑和实现方案。链接视角页面主要用于分析客户的复购行为和链接效果。

**最后更新时间**: 2025-08-02
**版本**: v2.1 (增加回购日期验证功能)

## 🏗️ 架构变更历史

### v1.0 (已废弃)
- 使用 `smart_admin_v3.crm_客户查询` 表存储预计算的客户数据
- 通过定时任务同步数据，存在数据滞后问题
- 客户分类逻辑依赖同步表中的 `客户类型` 字段

### v2.0 (当前版本)
- 直接从 `lirun` 数据库实时计算客户数据
- 移除对同步表的依赖，解决数据滞后问题
- 实现真正的实时数据分析

### v2.1 (最新版本)
- 新增回购日期验证功能
- 支持验证复购客户在指定回购期间的实际复购行为
- 提供验证后的复购率指标，提高数据准确性

## 🗄️ 数据库结构

### 主要数据源

#### lirun.订单明细 (核心数据表)
```sql
-- 关键字段说明
平台货品ID        VARCHAR    -- 商品唯一标识
客户唯一编码      VARCHAR    -- 客户唯一标识  
付款时间         DATETIME   -- 订单付款时间
付款金额         DECIMAL    -- 实际付款金额
已付            DECIMAL    -- 已付金额
订单状态         VARCHAR    -- 订单状态
订单来源         VARCHAR    -- 订单来源
标记名称         VARCHAR    -- 订单标记
```

### 数据过滤条件
```sql
-- 有效订单过滤条件
WHERE om.订单状态 != '线下退款'
  AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
  AND om.标记名称 != '贴贴贴贴贴'
```

## 🔍 核心查询逻辑

### 1. 客户分类逻辑 (新客/老客)

```sql
-- 实时客户分类计算
CASE 
    WHEN EXISTS (
        SELECT 1 FROM lirun.订单明细 hist 
        WHERE hist.客户唯一编码 = fo.客户唯一编码 
            AND hist.付款时间 < #{queryForm.startDate}
            AND hist.订单状态 != '线下退款'
            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
            AND hist.标记名称 != '贴贴贴贴贴'
    ) THEN '老客'
    ELSE '新客'
END as 客户类型
```

**分类规则**:
- **新客**: 在查询开始日期之前没有购买记录的客户
- **老客**: 在查询开始日期之前有购买记录的客户

### 2. 复购分析逻辑

```sql
-- 复购客户识别
SELECT 客户唯一编码
FROM lirun.订单明细 om
WHERE [基础过滤条件]
GROUP BY 客户唯一编码
HAVING COUNT(DISTINCT DATE(om.付款时间)) > 1
```

**复购定义**: 在指定时间范围内有多个不同购买日期的客户

### 3. 复购周期计算

```sql
-- 平均复购周期计算
SELECT 
    AVG(DATEDIFF(next_order_date, current_order_date)) as 平均复购周期
FROM (
    SELECT 
        客户唯一编码,
        付款时间 as current_order_date,
        LEAD(付款时间) OVER (
            PARTITION BY 客户唯一编码 
            ORDER BY 付款时间
        ) as next_order_date
    FROM lirun.订单明细
    WHERE [过滤条件]
) t
WHERE next_order_date IS NOT NULL
```

## 📊 主要查询接口

### 1. queryAnalysis - 链接分析统计

**功能**: 提供复购率、复购人数、付款人数等核心指标

**关键CTE结构**:
```sql
-- CTE 1: 基础订单过滤
FilteredOrders AS (...)

-- CTE 1.5: 回购日期期间客户筛选 (v2.1新增)
RepurchasePeriodCustomers AS (...)

-- CTE 2: 实时客户分类
FilteredOrdersWithCustomerType AS (...)

-- CTE 3: 客户汇总数据
CustomerSummary AS (...)

-- CTE 4: 复购客户识别
RepeatCustomers AS (...)

-- CTE 5: 回购日期验证 (v2.1新增)
RepurchaseValidation AS (...)
```

### 2. queryDetail - 复购明细

**功能**: 提供按复购次数分组的详细数据

**输出字段**:
- 复购次数
- 复购人数  
- 复购件数
- 复购金额
- 平均复购周期天数

### 3. downloadOrderDetail - 订单明细导出

**功能**: 导出符合条件的订单明细数据

**输出字段**: 订单的完整明细信息

## 🎯 查询参数

### CustomerLinkQueryForm 参数说明

```java
public class CustomerLinkQueryForm {
    private String customerType;       // 客户类型: "不限", "新客", "老客"
    private Date startDate;            // 付款开始日期
    private Date endDate;              // 付款结束日期
    private String repurchaseDateBegin; // 回购开始日期
    private String repurchaseDateEnd;   // 回购结束日期
    private String productId;          // 货品ID
    private List<String> excludeFlags; // 排除旗帜列表
}
```

### 参数映射到SQL

```sql
-- 客户类型过滤
<if test="queryForm.customerType == '新客'">
    AND NOT EXISTS (历史购买记录查询)
</if>
<if test="queryForm.customerType == '老客'">  
    AND EXISTS (历史购买记录查询)
</if>

-- 付款时间范围过滤
AND om.付款时间 >= #{queryForm.startDate}
AND om.付款时间 < #{queryForm.endDate}

-- 回购日期验证过滤（v2.1新增）
<if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateEnd != null">
    AND EXISTS (
        SELECT 1 FROM RepurchasePeriodCustomers rpc
        WHERE rpc.客户唯一编码 = om.客户唯一编码
    )
</if>

-- 货品ID过滤
<if test="queryForm.productId != null and queryForm.productId != ''">
    AND om.平台货品ID = #{queryForm.productId}
</if>
```

## ⚡ 性能优化

### 1. 索引建议

```sql
-- lirun.订单明细表建议索引
CREATE INDEX idx_product_payment_time ON 订单明细(平台货品ID, 付款时间);
CREATE INDEX idx_customer_payment_time ON 订单明细(客户唯一编码, 付款时间);
CREATE INDEX idx_payment_time ON 订单明细(付款时间);
```

### 2. 查询优化要点

- 使用CTE分步处理，提高可读性和性能
- EXISTS子查询用于客户分类，避免大表JOIN
- 合理使用索引覆盖查询
- 时间范围查询使用半开区间 `[start, end)`

## 🗑️ 已废弃的表结构

### smart_admin_v3.crm_客户查询 (已删除)
```sql
-- 此表已不再使用，可以安全删除
-- 原用途: 存储预计算的客户汇总数据
-- 废弃原因: 数据同步滞后，改为实时计算
```

### smart_admin_v3.t_customer_classification_log (已删除)  
```sql
-- 此表已不再使用，可以安全删除
-- 原用途: 记录客户分类任务执行日志
-- 废弃原因: 不再需要预计算任务
```

## 🔧 配置文件

### 数据源配置 (sa-base.yaml)
```yaml
spring:
  datasource:
    # 主数据源 - smart_admin_v3
    url: ******************************************
    
    # 外部数据源 - lirun (用于链接视角)
    external:
      url: *********************************
```

### MyBatis映射文件
- 位置: `smart-admin-api-java17-springboot3/sa-admin/src/main/resources/mapper/business/customer/CustomerLinkMapper.xml`
- 包含: queryAnalysis, queryDetail, downloadOrderDetail 三个主要查询

## 📈 数据验证示例

### 验证查询准确性
```sql
-- 验证付款人数
SELECT COUNT(DISTINCT 客户唯一编码) as 付款人数
FROM lirun.订单明细 om
WHERE om.平台货品ID = '779818444472'
    AND om.付款时间 >= '2025-07-23'
    AND om.付款时间 < '2025-07-30'
    AND om.订单状态 != '线下退款'
    AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
    AND om.标记名称 != '贴贴贴贴贴';

-- 验证复购人数  
SELECT COUNT(*) as 复购人数
FROM (
    SELECT 客户唯一编码
    FROM lirun.订单明细 om
    WHERE [同上过滤条件]
    GROUP BY 客户唯一编码
    HAVING COUNT(DISTINCT DATE(om.付款时间)) > 1
) t;
```

## 🚀 部署说明

1. **数据库连接**: 确保应用能同时连接 smart_admin_v3 和 lirun 数据库
2. **索引创建**: 在 lirun.订单明细 表上创建建议的索引
3. **配置更新**: 更新数据源配置文件
4. **代码部署**: 部署包含新查询逻辑的 CustomerLinkMapper.xml

## 📊 字段统计逻辑详解

### 核心统计指标

#### 1. 付款人数 (paymentUserCount)
**定义**: 在指定时间范围内，购买了筛选货品的去重客户数量。

**计算逻辑**:
- 从订单明细表中筛选出符合条件的订单记录
- 应用时间范围过滤：付款时间在查询开始日期和结束日期之间
- 应用货品过滤：平台货品ID在选中的货品列表中
- 应用订单状态过滤：排除线下退款、手工创建已取消、特定标记的订单
- 应用客服标旗过滤：排除用户选择的标旗订单
- 对客户唯一编码进行去重计数

#### 2. 复购人数 (repurchaseUserCount)
**定义**: 在指定时间范围内，对筛选货品有多天购买记录的客户数量。

**计算逻辑**:
- 基于付款人数的筛选条件，进一步分析每个客户的购买行为
- 按客户唯一编码分组，统计每个客户的购买天数
- 购买天数计算方式：对付款时间按日期去重后计数
- 筛选出购买天数大于等于2天的客户
- 对这些客户进行计数得到复购人数

#### 3. 复购率 (repurchaseRate)
**定义**: 复购客户占总付款客户的百分比，反映客户重复购买的比例。

**计算逻辑**:
- 复购率 = (复购人数 ÷ 付款人数) × 100%
- 结果保留两位小数
- 当付款人数为0时，复购率显示为0%

#### 3.1 验证复购率 (validatedRepurchaseRate) - v2.1新增
**定义**: 经过回购日期验证的复购率，只统计在指定回购期间确实有购买行为的客户。

**计算逻辑**:
- 验证复购率 = (验证复购人数 ÷ 付款人数) × 100%
- 验证复购人数：在回购日期期间内有购买行为的复购客户数
- 提供更精准的复购率指标，排除在回购期间无活动的客户

#### 4. 平均复购周期 (avgRepurchaseCycle)
**定义**: 复购客户从首次购买到最后一次购买的平均天数，反映客户复购的整体时间跨度。

**计算逻辑**:
- 仅针对复购客户（购买天数≥2）进行计算
- 对每个复购客户，计算其首次购买日期到最后一次购买日期的天数差
- 将所有复购客户的购买周期求平均值
- 结果保留两位小数

#### 5. 平均复购间隔 (avgRepurchaseInterval)
**定义**: 复购客户相邻两次购买之间的平均间隔天数，反映客户复购的频率。

**计算逻辑**:
- 仅针对复购客户的复购行为（排除首次购买）进行计算
- 对每个复购客户，按购买时间排序，计算相邻购买日期的间隔天数
- 将所有复购间隔求平均值
- 结果保留两位小数

#### 6. 最小复购间隔 (minRepurchaseInterval)
**定义**: 所有复购行为中，相邻两次购买间隔的最小天数。

**计算逻辑**:
- 计算所有复购客户的相邻购买间隔
- 取其中的最小值
- 结果为整数天数

#### 7. 最大复购间隔 (maxRepurchaseInterval)
**定义**: 所有复购行为中，相邻两次购买间隔的最大天数。

**计算逻辑**:
- 计算所有复购客户的相邻购买间隔
- 取其中的最大值
- 结果为整数天数

### 复购明细统计指标

#### 8. 复购次数分类 (repurchaseTimes)
**定义**: 按客户的复购次数进行分类统计。

**分类逻辑**:
- 第0次：在查询时间范围内只有1天购买记录的客户（非复购客户）
- 第1次：在查询时间范围内有2天购买记录的客户（复购1次）
- 第2次：在查询时间范围内有3天购买记录的客户（复购2次）
- 第N次：在查询时间范围内有N+1天购买记录的客户（复购N次）
- 合计：所有复购客户的汇总数据
- 人均：复购数据的人均值

#### 9. 各复购次数的人数 (repurchaseCustomers)
**定义**: 每个复购次数分类下的客户数量。

**计算逻辑**:
- 按复购次数分组，统计每组的去重客户数量
- 第0次：统计只有1天购买记录的客户数
- 第N次：统计有N+1天购买记录的客户数
- 合计：所有复购客户的总数

#### 10. 各复购次数的件数 (repurchaseQuantity)
**定义**: 每个复购次数分类下的商品购买件数。

**计算逻辑**:
- 第0次：只有1天购买记录客户的总购买件数
- 第N次：有N+1天购买记录客户的复购件数（排除首次购买件数）
- 合计：所有复购行为的总件数
- 人均：复购总件数除以复购客户数

#### 11. 各复购次数的金额 (repurchaseAmount)
**定义**: 每个复购次数分类下的购买金额。

**计算逻辑**:
- 第0次：只有1天购买记录客户的总购买金额
- 第N次：有N+1天购买记录客户的复购金额（排除首次购买金额）
- 合计：所有复购行为的总金额
- 人均：复购总金额除以复购客户数
- 结果保留两位小数

#### 12. 各复购次数的平均复购周期 (avgRepurchaseCycleDays)
**定义**: 每个复购次数分类下客户的平均复购间隔天数。

**计算逻辑**:
- 第0次：无复购行为，显示为空
- 第N次：该分类下所有复购间隔的平均值
- 合计：所有复购间隔的总平均值
- 人均：与合计相同
- 结果保留两位小数

### 客户分类逻辑

#### 13. 新客老客判断 (客户类型)
**定义**: 基于客户的历史购买记录判断其身份属性。

**判断逻辑**:
- 以查询开始日期为分界点
- 新客：在查询开始日期之前，该客户在系统中没有任何有效购买记录
- 老客：在查询开始日期之前，该客户在系统中已有有效购买记录
- 历史记录查询使用相同的订单过滤条件
- 注意：判断依据是客户的整体购买历史，不限于当前筛选的货品ID

### 数据过滤规则

#### 14. 有效订单定义
**过滤条件**:
- 订单状态不等于"线下退款"
- 排除订单来源为"手工创建"且订单状态为"已取消"的记录
- 排除标记名称为"贴贴贴贴贴"的记录
- 根据用户选择排除特定客服标旗的记录
- 货品ID、客户编码、付款时间等关键字段不能为空

#### 15. 时间范围处理
**处理逻辑**:
- 查询开始日期：包含该日期的00:00:00时刻
- 查询结束日期：不包含该日期，即到前一天的23:59:59时刻
- 使用半开区间设计，便于索引优化和边界处理

#### 16. 购买天数计算
**计算方式**:
- 将付款时间按日期部分去重
- 同一天内的多次购买只计算为1天
- 购买天数 = 去重后的购买日期数量
- 复购次数 = 购买天数 - 1

这些统计逻辑确保了数据的准确性和一致性，为业务分析提供了可靠的数据基础。

## 🔍 回购日期验证功能 (v2.1)

### 功能概述
回购日期验证功能允许用户指定一个回购期间，系统将验证复购客户在该期间内是否确实有购买行为，提供更精准的复购率分析。

### 核心逻辑
1. **双重筛选机制**：
   - 第一层：按付款日期范围筛选订单，识别复购客户
   - 第二层：按回购日期范围验证这些客户是否在指定期间有购买活动

2. **验证复购率计算**：
   - 只统计在回购期间确实有购买行为的复购客户
   - 排除在回购期间无活动的"沉默"客户
   - 提供 `validatedRepurchaseRate` 和 `validatedRepurchaseUserCount` 指标

3. **数据一致性**：
   - 分析统计、复购明细、订单下载三个查询保持一致的筛选逻辑
   - 确保所有相关数据都基于相同的客户范围

### 使用场景
- 验证营销活动期间的实际复购效果
- 分析特定时间段内客户的活跃度
- 提高复购率统计的准确性和业务价值

## 📞 联系信息

如有问题，请联系开发团队或查看相关代码文件：
- 前端: `smart-admin-web-javascript/src/views/business/customer/customer-link.vue`
- 后端: `smart-admin-api-java17-springboot3/sa-admin/src/main/java/net/lab1024/sa/admin/module/business/customer/`
- SQL: `CustomerLinkMapper.xml`
